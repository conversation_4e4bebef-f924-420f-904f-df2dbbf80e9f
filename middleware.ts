import { NextRequest, NextResponse } from 'next/server';

const locales = ['ar', 'en', 'fr'];

export function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Ignore static files and API routes
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/static') ||
    pathname === '/favicon.ico' ||
    /\.[^/]+$/.test(pathname)
  ) {
    return NextResponse.next();
  }

  // Redirect root to /ar
  if (pathname === '/') {
    const url = req.nextUrl.clone();
    url.pathname = '/ar';
    return NextResponse.redirect(url);
  }

  // Get the first segment after the leading slash
  const firstSegment = pathname.split('/')[1];

  // If the first segment is not a valid locale, rewrite to 404
  // Only allow exact matches: 'ar', 'en', 'fr'
  if (!locales.includes(firstSegment)) {
    return NextResponse.rewrite(new URL('/404', req.url));
  }

  // If the first segment is a partial match (e.g., 'ars'), also rewrite to 404
  // (This is already handled by the includes check, but let's be explicit)

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!_next|api|static|favicon.ico|.*\\..*).*)',
    '/',
  ],
};
