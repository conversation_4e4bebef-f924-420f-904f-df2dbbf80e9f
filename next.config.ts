import type { NextConfig } from "next";

const ContentSecurityPolicy = `
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://connect.facebook.net https://www.facebook.com https://www.youtube.com https://www.gstatic.com;
  style-src 'self' 'unsafe-inline' https://static.xx.fbcdn.net https://fonts.googleapis.com https://www.facebook.com https://*.fbcdn.net;
  img-src 'self' data: https://*.fbcdn.net https://*.facebook.com https://i.ytimg.com https://scontent.fcai20-2.fna.fbcdn.net https://bk.alahly.itechnologyeg.com https://alahly-images.s3.us-east-2.amazonaws.com https://img.youtube.com https://backend.alahlyegypt.com https://board.alahlyegypt.com;
  font-src 'self' https://fonts.gstatic.com;
  frame-src https://www.facebook.com https://www.youtube.com;
  media-src *;
  connect-src *;
`;

const securityHeaders = [
  {
    key: "Content-Security-Policy",
    value: ContentSecurityPolicy.replace(/\s{2,}/g, " ").trim(),
  },
  {
    key: "X-Frame-Options",
    value: "ALLOWALL",
  },
  {
    key: "Referrer-Policy",
    value: "strict-origin-when-cross-origin",
  },
  {
    key: "X-Content-Type-Options",
    value: "nosniff",
  },
  {
    key: "X-DNS-Prefetch-Control",
    value: "on",
  },
  {
    key: "Strict-Transport-Security",
    value: "max-age=31536000; includeSubDomains; preload",
  },
  {
    key: "Permissions-Policy",
    value:
      "camera=(), microphone=(), geolocation=(), autoplay=(self 'https://www.youtube.com' 'https://www.facebook.com')",
  },
];

const nextConfig: NextConfig = {
  async redirects() {
    return [
      {
        source: "/",
        destination: "/ar",
        permanent: true,
      },
    ];
  },
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: securityHeaders,
      },
    ];
  },
  images: {
    remotePatterns: [
      // Local development API
      {
        protocol: "http",
        hostname: "localhost",
        port: "8000",
        pathname: "/storage/**",
      },
      // Production API
      {
        protocol: "https",
        hostname: "backend.alahlyegypt.com",
        pathname: "/storage/**",
      },
      // Board API
      {
        protocol: "https",
        hostname: "board.alahlyegypt.com",
        pathname: "/storage/**",
      },
      // YouTube thumbnails
      {
        protocol: "https",
        hostname: "img.youtube.com",
        pathname: "/**",
      },
      // Placeholder images
      {
        protocol: "https",
        hostname: "via.placeholder.com",
        pathname: "/**",
      },
      // PNGWing CDN
      {
        protocol: "https",
        hostname: "w7.pngwing.com",
        pathname: "/**",
      },
      // S3 bucket
      {
        protocol: "https",
        hostname: "alahly-images.s3.us-east-2.amazonaws.com",
        pathname: "/**",
      },
      // Facebook CDN (wildcard for all subdomains and regions)
      {
        protocol: "https",
        hostname: "scontent.*.fbcdn.net",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "scontent-yyz1-1.xx.fbcdn.net",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "scontent.fcai1-2.fna.fbcdn.net",
        pathname: "/**",
      },
      // Instagram CDN (wildcard for all subdomains and regions)
      {
        protocol: "https",
        hostname: "scontent.*.cdninstagram.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "scontent-yyz1-1.cdninstagram.com",
        pathname: "/**",
      },
      // Instagram CDN (add root domain for direct subdomainless access)
      {
        protocol: "https",
        hostname: "scontent.cdninstagram.com",
        pathname: "/**",
      },
    ],
    // Optimize caching for images
    minimumCacheTTL: 60 * 60 * 24 * 7, // 1 week
    formats: ["image/webp", "image/avif"],
    // Optionally, set deviceSizes and imageSizes for better responsive behavior
    deviceSizes: [320, 420, 768, 1024, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // Optionally, set loader to 'default' for best compatibility
    loader: 'default',
  },
  reactStrictMode: true,
};

export default nextConfig;
