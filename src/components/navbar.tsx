"use client";

import Image from "next/image";
import Link from "next/link";
import navbarData from "@/data/navbar.json";
import translations from "@/lib/locales";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { useGlobalData } from "@/context/GlobalDataContext";
import MobileTopLinks from "@/components/navbar/MobileTopLinks";
import MobileBottomLinks from "@/components/navbar/MobileBottomLinks";
import TopLinks from "@/components/navbar/TopLinks";
import BottomLinks from "@/components/navbar/BottomLinks";
import SubNavbar from "@/components/navbar/SubNavbar";
import SearchModal from "@/components/SearchModal";

export default function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeSubNav, setActiveSubNav] = useState<number | null>(null);
  const { sports, top_banner } = useGlobalData();
  const pathname = usePathname();
  const locale = pathname ? pathname.split("/")[1] : "ar";
  const languages = [
    { code: "ar", label: "العربية" },
    { code: "en", label: "English" },
    { code: "fr", label: "Français" },
  ];
  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 10);
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  useEffect(() => {
    const foundIndex = navbarData.bottom.findIndex((item: any) =>
      item.sublinks && item.sublinks.some((link: any) => {
        const urlWithLocale = `/${locale}${link.url}`;
        // Match exact or any subroute (e.g. /academy/news)
        return pathname === urlWithLocale || pathname === `${urlWithLocale}/` || pathname.startsWith(urlWithLocale + "/");
      })
    );
    // Only close subnav for main /news page, not nested like /academy/news
    if (pathname && (pathname === `/${locale}/news`)) {
      setActiveSubNav(null);
      return;
    }
    // Make subnav active for academy link
    const academyIndex = navbarData.bottom.findIndex((item: any) => item.url === '/academy');
    if (pathname && pathname.startsWith(`/${locale}/academy`) && academyIndex !== -1) {
      setActiveSubNav(academyIndex);
    } else if (pathname && pathname.includes('/sports')) {
      const sportsIndex = navbarData.bottom.findIndex((item: any) => item.label === "sports");
      if (sportsIndex !== -1) setActiveSubNav(sportsIndex);
    } else if (foundIndex !== -1) {
      setActiveSubNav(foundIndex);
    }
  }, [pathname, locale]);
  const toggleSubNav = (index: number) => {
    setActiveSubNav(activeSubNav === index ? null : index);
  };
  const handleNavLinkClick = (url?: string, index?: number) => {
    if (typeof window !== 'undefined' && window.innerWidth < 1024) {
      setIsMobileMenuOpen(false);
      setActiveSubNav(null);
    }
    if (url && url.includes('/news')) {
      setActiveSubNav(null);
    } else if (typeof index === 'number') {
      setActiveSubNav(index);
    }
  };
  const [user, setUser] = useState<any>(null);
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const userStr = localStorage.getItem("user");
      if (userStr) {
        setUser(JSON.parse(userStr));
      }
    }
  }, [pathname]);
  const handleLogout = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem("access_token");
      localStorage.removeItem("user");
      setUser(null);
      window.location.href = `/${locale}/login`;
    }
  };
  const handleLanguageChange = (lang: string) => {
    if (lang === locale) return;
    const segments = pathname?.split("/") || [];
    segments[1] = lang;
    window.location.pathname = segments.join("/");
  };
  const [showTopBanner, setShowTopBanner] = useState(false);

  useEffect(() => {
    // Only show on home page: /[locale] or /[locale]/
    const isHome = pathname === `/${locale}` || pathname === `/${locale}/`;
    // Use sessionStorage for per-session close
    const bannerClosed = typeof window !== 'undefined' && sessionStorage.getItem('top_banner_closed') === '1';
    setShowTopBanner(!!top_banner && isHome && !bannerClosed);
  }, [pathname, locale, top_banner]);

  const handleCloseBanner = () => {
    setShowTopBanner(false);
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('top_banner_closed', '1');
      window.dispatchEvent(new Event('top_banner_closed'));
    }
  };

  return (
    <nav
      className={`fixed w-full text-white z-[1000000] transition-all duration-300 ${isScrolled ? "shadow-lg" : ""}`}
    >
      {showTopBanner && top_banner && (
        <div className="w-full h-fit relative bg-white flex justify-center items-center">
          <a href={top_banner.url || '#'} target="_blank" rel="noopener noreferrer" className="block w-full">
            <img src={top_banner.image} alt={top_banner.title} className="w-full object-contain" />
          </a>
          <button
            onClick={handleCloseBanner}
            className="absolute top-2 end-2 bg-black/60 text-white rounded-full w-10 h-10 flex items-center justify-center z-20 hover:bg-black/80 transition text-3xl"
            aria-label="Close banner"
          >
            &times;
          </button>
        </div>
      )}
      <div className="lg:hidden relative w-full h-16" style={{ background: 'linear-gradient(-145deg, #DFB254 0%, #C60428 35%, #7A0515 70%, #0A0A0A 100%)' }}>
        <div className="flex items-center w-full justify-between h-16">
          <Link href={`/${locale}`} prefetch={false} className="flex items-center h-full pt-12 ps-6">
            <Image
              src={"/logo.png"}
              alt="Al Ahly SC"
              width={70}
              height={56}
              className="relative"
            />
            <span className="ms-2 mt-4 text-xs font-bold text-white bg-[#C60428] px-2 py-1 rounded shadow-lg" style={{whiteSpace: 'nowrap'}}>
              {locale === 'ar' ? 'بث تجريبي' : locale === 'fr' ? 'Version bêta' : 'Beta Version'}
            </span>
          </Link>
          <div className="flex-1" />
          <div className="flex gap-4 items-center justify-end">
            {languages.filter((lang) => lang.code !== locale).map((lang) => (
              <button
                key={lang.code}
                onClick={() => handleLanguageChange(lang.code)}
                className="px-2 py-1 rounded text-xs font-bold border border-[#DFB254] bg-transparent text-white hover:bg-[#DFB254] hover:text-[#C60428] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#DFB254]"
                aria-label={lang.label}
                style={{ cursor: 'pointer', position: 'relative', zIndex: 10000 }}
                tabIndex={0}
              >
                {lang.label}
              </button>
            ))}
          </div>
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="text-white focus:outline-none z-50 flex items-center justify-center h-12 w-12 ms-4"
            style={{ alignSelf: 'center' }}
          >
            <svg
              className="w-8 h-8"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              {isMobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>
      </div>
      {isMobileMenuOpen && (
        <div className="lg:hidden bg-[#C60428] w-full py-12 px-4 overflow-y-auto max-h-[100dvh]">
          <MobileTopLinks
            links={navbarData.top}
            locale={locale}
            translations={translations}
            handleNavLinkClick={handleNavLinkClick}
            setIsMobileMenuOpen={setIsMobileMenuOpen}
            setIsSearchModalOpen={setIsSearchModalOpen}
          />
          <MobileBottomLinks
            navbarData={navbarData}
            locale={locale}
            translations={translations}
            sports={sports}
            activeSubNav={activeSubNav}
            toggleSubNav={toggleSubNav}
            handleNavLinkClick={handleNavLinkClick}
            setIsMobileMenuOpen={setIsMobileMenuOpen}
          />

          {/* Search Modal */}
          <SearchModal
            isOpen={isSearchModalOpen}
            onClose={() => setIsSearchModalOpen(false)}
            locale={locale}
            translations={translations}
          />
        </div>
      )}
      <div className="h-[110px] absolute start-0 lg:start-96 z-10 overflow-hidden">
        <Image
          src={"/eagle.svg"}
          alt="Ahly Eagle"
          width={400}
          height={110}
          style={{ objectFit: 'cover', objectPosition: 'center' }}
          className="hidden lg:block"
        />
      </div>
      <div className="hidden lg:flex w-full h-[75px] px-2 lg:px-4 justify-end items-center" style={{ background: 'linear-gradient(-145deg, #DFB254 0%, #C60428 35%, #7A0515 70%, #0A0A0A 100%)' }}>
        <TopLinks
          links={navbarData.top}
          locale={locale}
          translations={translations}
          user={user}
        />
        <div className="flex items-center gap-2 ms-4 z-50">
          {user ? (
            <>
              <Link
                href={`/${locale}/profile`}
                className="flex items-center gap-2 font-bold text-[#DFB254] hover:underline"
              >
                <Image src={user.profile_picture || "/logo.png"} alt="User" width={32} height={32} className="rounded-full bg-white" />
                {user.name || user.email}
              </Link>
              <button
                onClick={handleLogout}
                className="ml-2 px-3 py-1 rounded bg-[#DFB254] text-[#C60428] font-bold hover:bg-[#F8D384] transition-colors duration-200 border border-[#DFB254]"
              >
                {translations[locale]?.logout || "Logout"}
              </button>
            </>
          ) : null}
        </div>
        <div className="flex gap-2 items-end ms-4 z-50">
          {languages.filter((lang) => lang.code !== locale).map((lang) => (
            <button
              key={lang.code}
              onClick={() => handleLanguageChange(lang.code)}
              className="px-2 py-1 rounded text-xs font-bold border border-[#DFB254] bg-transparent text-white hover:bg-[#DFB254] hover:text-[#C60428] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#DFB254]"
              aria-label={lang.label}
              style={{ cursor: 'pointer', position: 'relative', zIndex: 10000 }}
              tabIndex={0}
            >
              {lang.label}
            </button>
          ))}
        </div>
      </div>
      <div className="hidden lg:flex w-full h-[38px] px-4 lg:px-20 justify-between items-center" style={{ background: 'linear-gradient(-145deg, #DFB254 0%, #C60428 35%, #7A0515 70%, #0A0A0A 100%)' }}>
        <div className="flex justify-start items-center gap-6 min-w-[180px] relative">
          <Link href={`/${locale}`} prefetch={false} className="flex items-center relative">
            <Image
              src={"/logo.png"}
              alt="Al Ahly Logo"
              width={150}
              height={120}
              className="z-50 relative"
            />
            <span className="ms-2 text-xs font-bold text-white bg-[#C60428] px-2 py-1 rounded shadow-lg" style={{whiteSpace: 'nowrap', position: 'static'}}>
              {locale === 'ar' ? 'بث تجريبي' : locale === 'fr' ? 'Version bêta' : 'Beta Version'}
            </span>
          </Link>
        </div>
        <div className="flex justify-end items-center">
          <BottomLinks
            navbarData={navbarData}
            locale={locale}
            translations={translations}
            pathname={pathname}
            activeSubNav={activeSubNav}
            toggleSubNav={toggleSubNav}
            handleNavLinkClick={handleNavLinkClick}
          />
        </div>
      </div>
      <SubNavbar
        activeSubNav={activeSubNav}
        navbarData={navbarData}
        locale={locale}
        pathname={pathname}
        translations={translations}
        sports={sports}
        handleNavLinkClick={handleNavLinkClick}
      />
    </nav>
  );
}

