import Image from 'next/image';
import Link from 'next/link';
import translations from '@/lib/locales';
import { useState } from 'react';

export interface NewsItem {
    id: number;
    title: string;
    slug: string;
    intro: string;
    image: string;
    image_full: string;
    video_url: string;
    published_at: string;
    category?: {
        title: string;
    };
    sub_category?: {
        title: string;
    };
    gallery?: { id: number; news_id: number; image: string }[];
}

export default function NewsCard({ item, locale, type = '1' }: { item: NewsItem; locale: string; type?: string }) {
    const [showModal, setShowModal] = useState(false);
    const [galleryIndex, setGalleryIndex] = useState(0);
    const [imgLoading, setImgLoading] = useState(true);
    const [imgSrc, setImgSrc] = useState(item.image_full || '/img/default_placeholder.png');
    // VIDEO TYPE
    if (type === '2') {
        const dateObj = new Date(item.published_at);
        const day = dateObj.getDate();
        const month = dateObj.toLocaleString(locale.replace('_', '-'), { month: 'long' });
        const youtubeId = item.video_url;
        return (
            <>
                <div className="bg-white rounded-[5px] shadow-[0px_4px_10px_rgba(0,0,0,0.1)] flex flex-col overflow-hidden border border-[#E0E0E0] p-0" style={{ minHeight: 0 }}>
                    <div className="relative h-[220px] flex items-center justify-center bg-[#E0E0E0] cursor-pointer" onClick={() => setShowModal(true)}>
                        <img src={item.image_full} alt={item.title} className="absolute inset-0 w-full h-full object-cover opacity-80" />
                        {/* Play icon overlay */}
                        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" className="absolute z-10">
                            <circle cx="32" cy="32" r="32" fill="#fff" fillOpacity="0.5" />
                            <polygon points="26,20 48,32 26,44" fill="#272525" />
                        </svg>
                        {/* Date overlay */}
                        <div className="absolute top-4 left-4 flex flex-col items-center">
                            <span className="font-cairo text-2xl font-extrabold text-[#BDBDBD] leading-none">{day}</span>
                            <span className="text-xs text-[#BDBDBD] mt-1">{month}</span>
                        </div>
                    </div>
                    <div className="p-4 pb-2 pt-4 text-center text-[#272525] font-cairo text-base font-bold leading-[1.7]">
                        {item.title}
                    </div>
                </div>
                {showModal && (
                    <div
                        className="fixed inset-0 z-[1000002] flex items-center justify-center"
                        style={{ background: 'rgba(0,0,0,0.4)' }}
                        onClick={() => setShowModal(false)}
                        tabIndex={-1}
                        onKeyDown={e => { if (e.key === 'Escape') setShowModal(false); }}
                    >
                        <div
                            className="bg-white rounded-lg overflow-hidden relative w-full max-w-4xl"
                            style={{ maxWidth: '900px' }}
                            onClick={e => e.stopPropagation()}
                        >
                            <button
                                className="absolute top-4 left-4 text-black text-3xl z-10 bg-white bg-opacity-80 rounded-full w-10 h-10 flex items-center justify-center shadow hover:bg-opacity-100 transition"
                                onClick={() => setShowModal(false)}
                                aria-label="Close"
                            >
                                &times;
                            </button>
                            <div className="w-full" style={{ aspectRatio: '16/9' }}>
                                <iframe
                                    width="100%"
                                    height="500"
                                    src={`${youtubeId}`}
                                    title={item.title}
                                    frameBorder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowFullScreen
                                />
                            </div>
                        </div>
                    </div>
                )}
            </>
        );
    }
    // GALLERY TYPE
    if (type === '3') {
        const dateObj = new Date(item.published_at);
        const day = dateObj.getDate();
        const month = dateObj.toLocaleString(locale.replace('_', '-'), { month: 'long' });
        const mainImage = item.image || (item.gallery && item.gallery[0]?.image) || '';
        const galleryImages = item.gallery?.map(g => g.image) || (mainImage ? [mainImage] : []);
        return (
            <>
                <div className="bg-white rounded-[5px] shadow-[0px_4px_10px_rgba(0,0,0,0.1)] flex flex-col overflow-hidden border border-[#E0E0E0] p-0" style={{ minHeight: 0 }}>
                    <div className="relative h-[220px] flex items-center justify-center bg-[#E0E0E0] cursor-pointer" onClick={() => { setGalleryIndex(0); setShowModal(true); }}>
                        <img src={mainImage} alt={item.title} className="absolute inset-0 w-full h-full object-cover opacity-80" />
                        {/* Folder icon overlay */}
                        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" className="absolute z-10">
                            <circle cx="32" cy="32" r="32" fill="#fff" fillOpacity="0.5" />
                            <g>
                                <rect x="20" y="32" width="24" height="10" rx="2" fill="#BDBDBD" />
                                <rect x="24" y="26" width="16" height="10" rx="2" fill="#BDBDBD" />
                            </g>
                        </svg>
                        {/* Date overlay */}
                        <div className="absolute top-4 left-4 flex flex-col items-center">
                            <span className="font-cairo text-2xl font-extrabold text-[#BDBDBD] leading-none">{day}</span>
                            <span className="text-xs text-[#BDBDBD] mt-1">{month}</span>
                        </div>
                    </div>
                    <div className="p-4 pb-2 pt-4 text-center text-[#272525] font-cairo text-base font-bold leading-[1.7]">
                        {item.title}
                    </div>
                </div>
                {showModal && (
                    <div
                        className="fixed inset-0 z-[1000002] flex items-center justify-center"
                        style={{ background: 'rgba(0,0,0,0.7)' }}
                        onClick={() => setShowModal(false)}
                        tabIndex={-1}
                        onKeyDown={e => { if (e.key === 'Escape') setShowModal(false); }}
                    >
                        <div
                            className="bg-white rounded-lg overflow-hidden relative w-full max-w-4xl flex flex-col items-center"
                            style={{ maxWidth: '900px' }}
                            onClick={e => e.stopPropagation()}
                        >
                            <button
                                className="absolute top-4 left-4 text-black text-3xl z-10 bg-white bg-opacity-80 rounded-full w-10 h-10 flex items-center justify-center shadow hover:bg-opacity-100 transition"
                                onClick={() => setShowModal(false)}
                                aria-label="Close"
                            >
                                &times;
                            </button>
                            <div className="relative w-full flex items-center justify-center" style={{ minHeight: 400 }}>
                                {galleryIndex > 0 && (
                                    <button
                                        className="absolute left-2 top-1/2 -translate-y-1/2 bg-[#EC2028] text-white rounded-full w-10 h-10 flex items-center justify-center z-10"
                                        onClick={() => setGalleryIndex(galleryIndex - 1)}
                                        aria-label="Previous image"
                                    >
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M15 18l-6-6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>
                                    </button>
                                )}
                                <img src={galleryImages[galleryIndex]} alt={item.title} className="w-auto max-h-[70vh] object-contain mx-auto" />
                                {galleryIndex < galleryImages.length - 1 && (
                                    <button
                                        className="absolute right-2 top-1/2 -translate-y-1/2 bg-[#BDBDBD] text-white rounded-full w-10 h-10 flex items-center justify-center z-10"
                                        onClick={() => setGalleryIndex(galleryIndex + 1)}
                                        aria-label="Next image"
                                    >
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M9 6l6 6-6 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>
                                    </button>
                                )}
                            </div>
                            {galleryImages.length > 1 && (
                                <div className="flex justify-center gap-2 mt-4 mb-2">
                                    {galleryImages.map((img, idx) => (
                                        <button
                                            key={img}
                                            className={`w-3 h-3 rounded-full border ${galleryIndex === idx ? 'bg-[#EC2028]' : 'bg-gray-300'}`}
                                            onClick={() => setGalleryIndex(idx)}
                                            aria-label={`عرض الصورة رقم ${idx + 1}`}
                                        />
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </>
        );
    }
    return (
        <Link
            key={item.id}
            href={`/${locale}/news/article/${item.slug}`}
            className="group relative bg-white rounded-[5px] shadow-[0px_4px_10px_rgba(0,0,0,0.1)] hover:shadow-lg transition-all overflow-hidden flex flex-col h-[345px]"
        >
            {/* Image Section */}
            <div className="relative h-[225px] overflow-hidden">
                <Image
                    src={imgSrc}
                    alt={item.title}
                    fill
                    className={`object-cover group-hover:scale-105 transition-transform ${imgLoading ? 'opacity-50' : 'opacity-100'}`}
                    onError={() => setImgSrc('/img/default_placeholder.png')}
                    onLoadingComplete={() => setImgLoading(false)}
                />
                {imgLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white/60 z-10">
                        <svg className="animate-spin h-8 w-8 text-[#C60428]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                        </svg>
                    </div>
                )}
                {/* Image Overlay Elements */}
                <div className="absolute top-4 left-4 flex flex-col">
                    <span className="font-cairo text-2xl font-black text-white">
                        {new Date(item.published_at).getDate()}
                    </span>
                    <span className="text-[10px] text-white">
                        {new Date(item.published_at).toLocaleString(locale.replace('_', '-'), { month: 'long' })}
                    </span>
                </div>
                <div className="absolute top-[179px] right-4 bg-[#D10128] px-4 py-1 rounded">
                    <span className="text-xs text-white">
                        {item.category?.title || ''}
                    </span>
                </div>
                <div className="absolute bottom-2 left-4 flex items-center gap-2 text-white text-xs">
                    <span>{item.published_at?.split('T')[1]?.slice(0, 5)}</span>
                    {item.sub_category?.title && (
                        <>
                            <span className="w-1 h-1 bg-white rounded-full" />
                            <span>{item.sub_category?.title || ''}</span>
                        </>
                    )}
                </div>

            </div>
            {/* Content Section */}

            <div className="flex-1 p-4 flex flex-col justify-between">
                <h3 className="font-hacen-algeria-bd text-[20px] leading-[150%] text-[#272525] text-start truncate whitespace-nowrap overflow-hidden">
                    {item.title}
                </h3>
                <div className="border-t border-[#E0E0E0] pt-3">
                    <button className="text-[#D10128] hover:text-red-800 text-sm flex items-center gap-1 float-end cursor-pointer">
                        {translations[locale]?.details || 'التفاصيل'}
                        <svg
                            width="12"
                            height="12"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M7.29297 14.707C7.10547 14.8945 7.00001 15.1488 7.00001 15.414C7.00001 15.6792 7.10547 15.9335 7.29297 16.121C7.48051 16.3085 7.73482 16.414 8.00001 16.414C8.26521 16.414 8.51951 16.3085 8.70701 16.121L12.707 12.121C12.8945 11.9335 13 11.6792 13 11.414C13 11.1488 12.8945 10.8945 12.707 10.707L8.70701 6.70703C8.51845 6.52222 8.2658 6.41943 8.00361 6.42133C7.74142 6.42324 7.49062 6.52971 7.3053 6.71754C7.11997 6.90538 7.0149 7.15912 7.013 7.42131C7.01109 7.6835 7.11251 7.93613 7.29297 8.12103L10.586 11.414L7.29297 14.707Z"
                                fill="currentColor"
                            />
                        </svg>
                    </button>
                </div>
            </div>
        </Link>
    );
}
