import React, { useState } from "react";
import Image from "next/image";

export default function SocialCard({
  socialIcon,
  profilePicture,
  profileName,
  postText,
  postMedia,
  postStats,
  postDateTime,
  cardHeight = 600, // Facebook/Instagram card height (px)
  postMediaHeight = 400, // Facebook/Instagram post image/video height (px)
}: any) {
  return (
    <div
      className={
        `relative w-full max-w-[420px] rounded-2xl flex flex-col gap-2 bg-gradient-to-b from-[#484848] to-[#232323] p-0 mx-auto items-center justify-center border border-[#222] shadow-xl overflow-hidden transition-all duration-300 hover:scale-[1.025]`
      }
      style={{ minHeight: 340, boxShadow: '0 8px 32px 0 rgba(0,0,0,0.18)' }}
    >
      <img
        src={socialIcon}
        alt="social platform"
        className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 absolute top-4 right-4 z-10 drop-shadow-lg"
      />
      <div className="flex flex-row-reverse items-center justify-center gap-3 sm:gap-4 mt-6 w-full px-4 pt-4">
        <div className="w-12 h-12 flex-shrink-0 bg-white flex justify-center items-center rounded-full overflow-hidden border-2 border-[#e6c97b] shadow">
          <div className="w-full h-full flex items-center justify-center">
            {profilePicture}
          </div>
        </div>
        <span className="text-base sm:text-lg md:text-xl font-bold text-[#e6c97b] drop-shadow">
          {profileName}
        </span>
      </div>
      <div className="text-sm sm:text-base mt-2 line-clamp-3 text-center w-full min-h-[48px] flex items-center justify-center px-4 text-[#f6f6f6]">
        {postText}
      </div>
      <div className="mt-2 w-full flex-1 flex flex-col items-center justify-center px-0">
        <FadeInMedia>{postMedia}</FadeInMedia>
      </div>
    </div>
  );
}

function FadeInMedia({ children }: { children: React.ReactNode }) {
  const [loaded, setLoaded] = useState(false);

  // Only apply fade-in to next/image or img
  if (React.isValidElement(children)) {
    // For <a> wrappers
    if (
      children.type === 'a' &&
      React.isValidElement((children as any).props.children)
    ) {
      const inner = (children as any).props.children;
      if (inner.type === 'div' && React.isValidElement((inner as any).props.children)) {
        const img = (inner as any).props.children;
        if (img && (img.type === 'img' || img.type === Image)) {
          return React.cloneElement(children, {
            children: React.cloneElement(inner, {
              children: React.cloneElement(img, {
                className: (img.props.className || '') + ` transition-opacity duration-700 ${loaded ? 'opacity-100' : 'opacity-0'}`,
                onLoad: (e: any) => {
                  setLoaded(true);
                  if (img.props.onLoad) img.props.onLoad(e);
                },
              })
            })
          } as any);
        }
      }
    }
    // For direct img or next/image
    if (children.type === 'img' || children.type === Image) {
      const c: any = children;
      return React.cloneElement(c, {
        className: (c.props.className || '') + ` transition-opacity duration-700 ${loaded ? 'opacity-100' : 'opacity-0'}`,
        onLoad: (e: any) => {
          setLoaded(true);
          if (c.props.onLoad) c.props.onLoad(e);
        },
      });
    }
  }
  return children;
}
