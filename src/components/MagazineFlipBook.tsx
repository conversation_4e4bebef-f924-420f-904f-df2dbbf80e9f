import React, { useState, useRef } from 'react';
import HTMLFlipBook from 'react-pageflip';
import { pdfjs, Document, Page } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';

interface MagazineFlipBookProps {
    pdfUrl: string;
}

const MagazineFlipBook: React.FC<MagazineFlipBookProps> = ({ pdfUrl }) => {
    const [numPages, setNumPages] = useState<number>(0);
    const [loading, setLoading] = useState(true);
    const flipBookRef = useRef<any>(null);

    function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
        setNumPages(numPages);
        setLoading(false);
    }

    function handleFullScreen() {
        let elem = null;
        if (flipBookRef.current) {
            elem = flipBookRef.current.el;
            if (!elem && flipBookRef.current.children && flipBookRef.current.children[0]) {
                elem = flipBookRef.current.children[0];
            }
        }
        // Helper to try all vendor-prefixed fullscreen methods
        function requestFullscreen(target: any) {
            if (!target) return false;
            if (target.requestFullscreen) return target.requestFullscreen();
            // @ts-ignore
            if (target.webkitRequestFullscreen) return target.webkitRequestFullscreen();
            // @ts-ignore
            if (target.mozRequestFullScreen) return target.mozRequestFullScreen();
            // @ts-ignore
            if (target.msRequestFullscreen) return target.msRequestFullscreen();
            return false;
        }
        if (!requestFullscreen(elem)) {
            // Fallback: try .flipbook class
            const fallbackElem = document.querySelector('.flipbook');
            if (!requestFullscreen(fallbackElem)) {
                alert('Fullscreen API is not supported in this browser.');
            }
        }
    }

    return (
        <>
            {/* Fullscreen Icon Button Overlay */}
            {!loading && (
                <button
                    onClick={handleFullScreen}
                    className="fixed top-6 right-6 z-[100] bg-black/60 hover:bg-black/80 text-white rounded-full p-3 shadow-lg transition"
                    title="Full Screen"
                    style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                >
                    {/* SVG Fullscreen Icon */}
                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" d="M9 3H5a2 2 0 0 0-2 2v4m0 6v4a2 2 0 0 0 2 2h4m6-18h4a2 2 0 0 1 2 2v4m0 6v4a2 2 0 0 1-2 2h-4"/></svg>
                </button>
            )}
            {loading && <div className="text-center py-4">Loading magazine...</div>}
            <Document
                file={pdfUrl}
                onLoadSuccess={onDocumentLoadSuccess}
                loading={null}
            >
                {numPages > 0 && (
                    <HTMLFlipBook
                        ref={flipBookRef}
                        width={window.innerWidth}
                        height={window.innerHeight * 0.8}
                        size="stretch"
                        minWidth={315}
                        maxWidth={window.innerWidth}
                        minHeight={400}
                        maxHeight={window.innerHeight * 0.8}
                        drawShadow={true}
                        showCover={true}
                        mobileScrollSupport={true}
                        className="flipbook rtl-flipbook"
                        style={{ margin: '0 auto', direction: 'rtl' }}
                        startPage={numPages - 1}
                        flippingTime={600}
                        usePortrait={true}
                        startZIndex={0}
                        autoSize={true}
                        maxShadowOpacity={0.5}
                        showPageCorners={true}
                        disableFlipByClick={false}
                        clickEventForward={false}
                        useMouseEvents={true}
                        swipeDistance={30}
                    >
                        {Array.from(new Array(numPages), (el, index) => (
                            <div
                                key={`page_${numPages - index}`}
                                style={{
                                    width: '100%',
                                    height: '100%',
                                    background: '#fff',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    padding: 0,
                                    margin: 0,
                                    direction: 'rtl',
                                }}
                            >
                                <Page
                                    pageNumber={numPages - index}
                                    width={window.innerWidth / 2} // Always use the same width for all pages
                                    height={window.innerHeight * 0.8}
                                    renderAnnotationLayer={false}
                                    renderTextLayer={true}
                                />
                            </div>
                        ))}
                    </HTMLFlipBook>
                )}
            </Document>
        </>
    );
};

export default MagazineFlipBook;
