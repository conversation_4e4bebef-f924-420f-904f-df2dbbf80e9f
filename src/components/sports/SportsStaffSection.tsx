import Image from "next/image";

interface SportsStaffSectionProps {
  staff: any[];
  t: (key: string) => string;
}

export default function SportsStaffSection({ staff, t }: SportsStaffSectionProps) {
  if (!staff || staff.length === 0) return null;
  return (
    <section className="w-full p-4 sm:p-6 md:p-8 text-white mt-12">
      <h5 className="text-xl sm:text-2xl md:text-3xl font-bold text-start mb-8">{t("technical_staff") || "الجهاز الفني"}</h5>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-6 justify-center">
        {staff.map((member: any) => (
          <div key={member.id} className="relative flex flex-col w-full h-[260px] bg-[#181818] rounded-lg overflow-hidden shadow-lg">
            <Image
              src={member.image || "/players/1.png"}
              alt={member.name}
              fill
              className="object-cover object-center w-full h-full"
              style={{ minHeight: 180 }}
            />
            {/* Overlay for name and position */}
            <div className="absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/90 via-black/60 to-transparent px-3 py-4 text-center">
              <div className="font-bold text-lg text-[#DFB254] mb-1">{member.name}</div>
              <div className="text-gray-200 text-sm">{member.position}</div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
