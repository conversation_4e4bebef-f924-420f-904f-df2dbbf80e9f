'use client';
import { useEffect, useState, useCallback } from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import NewsCard from './news-card';
import { fetchApi } from '@/lib/api';
import translations from '@/lib/locales';

interface NewsItem {
    id: number;
    title: string;
    slug: string;
    intro: string;
    image: string;
    image_full: string;
    video_url: string;
    published_at: string;
    category?: {
        title: string;
    };
}

interface Category {
    id: number;
    slug: string;
    title: string;
}

interface NewsGridProps {
    locale: string;
    title?: string;
    categories?: Category[];
    displayCategories?: boolean;
    initialType?: string; // Added for API query param
    initialNews?: NewsItem[]; // Added for initial news
    initialPagination?: any; // Added for initial pagination
}

export default function NewsPage({ locale, title, initialNews, initialPagination }: NewsGridProps) {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();

    // Get the current page from URL or default to 1
    const getCurrentPageFromUrl = useCallback(() => {
        if (!searchParams) return 1;
        const page = searchParams.get('page');
        return page ? parseInt(page as string) : 1;
    }, [searchParams]);

    const [news, setNews] = useState<NewsItem[]>(initialNews || []);
    const [page, setPage] = useState<number>(getCurrentPageFromUrl());
    const [lastPage, setLastPage] = useState<number>(initialPagination?.last_page || 1);
    const [isFirstRender, setIsFirstRender] = useState(true);
    const [loading, setLoading] = useState(false);

    // Fetch news data
    const fetchNews = useCallback(async (pageNumber: number) => {
        setLoading(true);
        let url = `/api/sports/youth_sector?page=${pageNumber}`;
        try {
            const res = await fetchApi(url, {
                headers: { "X-localization": locale },
            });
            setNews(
                (res.news || []).map((item: any) => ({
                    ...item,
                    video_url: typeof item.video_url === 'string' ? item.video_url : '',
                }))
            );
            setLastPage(res.pagination?.last_page || 1);
        } catch (e) {
            setNews([]);
            setLastPage(1);
        } finally {
            setLoading(false);
        }
    }, [locale]);

    // Update page state when URL changes (including back button)
    useEffect(() => {
        const currentPage = getCurrentPageFromUrl();
        setPage(currentPage);

        // Skip API call on first render if we have initialNews and we're on page 1
        if (isFirstRender && initialNews && currentPage === 1) {
            setIsFirstRender(false);
            return;
        }

        // Only fetch on mount or if searchParams changes (not on page change)
        if (currentPage !== page) {
            fetchNews(currentPage);
        }
    }, [searchParams, getCurrentPageFromUrl, initialNews, isFirstRender]);

    // Function to handle page changes (client-side fetch, no navigation)
    const handlePageChange = useCallback((newPage: number) => {
        setPage(newPage);
        fetchNews(newPage);
        // Optionally update the URL without reload (shallow routing)
        if (window && window.history && pathname) {
            const params = searchParams ? new URLSearchParams(searchParams.toString()) : new URLSearchParams();
            params.set('page', newPage.toString());
            window.history.replaceState({}, '', `${pathname}?${params.toString()}`);
        }
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }, [fetchNews, pathname, searchParams]);

    return (
        <section className="container mx-auto px-4">
            {/* Title and Top Pagination */}
            <div className="flex items-center justify-between mb-4">
                {title && <h2 className="text-2xl font-bold text-[#272525]">{title}</h2>}

                {lastPage > 1 && (
                    <div className="flex gap-1">
                        <button
                            disabled={page <= 1}
                            onClick={() => handlePageChange(page - 1)}
                            className={`px-3 py-1 border rounded ${page <= 1 ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                        >
                            &lt;
                        </button>

                        {/* Show limited page numbers */}
                        {[...Array(lastPage)].map((_, i) => {
                            const pageNum = i + 1;
                            // Only show: first, last, current, and 1 page before/after current
                            if (
                                pageNum === 1 ||
                                pageNum === lastPage ||
                                pageNum === page ||
                                pageNum === page - 1 ||
                                pageNum === page + 1
                            ) {
                                return (
                                    <button
                                        key={i}
                                        onClick={() => handlePageChange(pageNum)}
                                        className={`px-3 py-1 rounded ${page === pageNum ? 'bg-[#EC2028] text-white' : 'border'} ${page === pageNum ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                                        disabled={page === pageNum}
                                    >
                                        {pageNum}
                                    </button>
                                );
                            }

                            // Show ellipsis (but only once between gaps)
                            if ((pageNum === 2 && page > 3) || (pageNum === lastPage - 1 && page < lastPage - 2)) {
                                return <span key={i} className="px-2 py-1">...</span>;
                            }

                            return null;
                        })}

                        <button
                            disabled={page >= lastPage}
                            onClick={() => handlePageChange(page + 1)}
                            className={`px-3 py-1 border rounded ${page >= lastPage ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                        >
                            &gt;
                        </button>
                    </div>
                )}
            </div>

            {/* News Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {loading ? (
                    Array.from({ length: 9 }).map((_, i) => (
                        <div key={i} className="col-span-1 animate-pulse bg-gray-200 rounded-lg h-64 w-full">
                            <div className="h-40 bg-gray-300 rounded-t-lg shimmer" />
                            <div className="p-4">
                                <div className="h-4 bg-gray-300 rounded w-3/4 mb-2 shimmer" />
                                <div className="h-3 bg-gray-300 rounded w-1/2 mb-1 shimmer" />
                                <div className="h-3 bg-gray-200 rounded w-1/3 shimmer" />
                            </div>
                        </div>
                    ))
                ) : Array.isArray(news) && news.length === 0 ? (
                    <div className="col-span-3 text-center text-gray-500 py-12 text-lg font-cairo">
                        {translations[locale]?.no_news || 'لا توجد أخبار'}
                    </div>
                ) : (
                    (Array.isArray(news) ? news : []).map((item) => (
                        <NewsCard key={item.id} item={item} locale={locale} />
                    ))
                )}
            </div>

            {/* Bottom Pagination */}
            {lastPage > 1 && (
                <div className="flex justify-center mt-8 gap-1">
                    <button
                        disabled={page <= 1}
                        onClick={() => handlePageChange(page - 1)}
                        className={`px-3 py-1 border rounded ${page <= 1 ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                    >
                        &lt;
                    </button>

                    {/* Show limited page numbers */}
                    {[...Array(lastPage)].map((_, i) => {
                        const pageNum = i + 1;
                        if (
                            pageNum === 1 ||
                            pageNum === lastPage ||
                            pageNum === page ||
                            pageNum === page - 1 ||
                            pageNum === page + 1
                        ) {
                            return (
                                <button
                                    key={i}
                                    onClick={() => handlePageChange(pageNum)}
                                    className={`px-3 py-1 rounded ${page === pageNum ? 'bg-[#EC2028] text-white' : 'border'} ${page === pageNum ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                                    disabled={page === pageNum}
                                >
                                    {pageNum}
                                </button>
                            );
                        }

                        if ((pageNum === 2 && page > 3) || (pageNum === lastPage - 1 && page < lastPage - 2)) {
                            return <span key={i} className="px-2 py-1">...</span>;
                        }

                        return null;
                    })}

                    <button
                        disabled={page >= lastPage}
                        onClick={() => handlePageChange(page + 1)}
                        className={`px-3 py-1 border rounded ${page >= lastPage ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                    >
                        &gt;
                    </button>
                </div>
            )}
        </section>
    );
}
