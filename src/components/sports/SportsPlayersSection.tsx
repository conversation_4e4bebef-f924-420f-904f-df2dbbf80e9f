import TeamSection from "@/components/team-section";

interface SportsPlayersSectionProps {
  players: any[];
  t: (key: string) => string;
}

export default function SportsPlayersSection({ players, t }: SportsPlayersSectionProps) {
  if (!players || players.length === 0) return null;
  return (
    <section className="w-full p-4 sm:p-6 md:p-8 text-white mt-12">
      <h5 className={"text-xl sm:text-2xl md:text-3xl"}>{t("players_list") || "قائمة اللاعبين"}</h5>
      <div className="flex flex-wrap gap-6 justify-center">
        <TeamSection players={players} />
      </div>
    </section>
  );
}
