interface SportsNewsSectionProps {
  news: any[];
  locale: string;
  t: (key: string) => string;
}
import RelatedNewsCarousel from "@/components/RelatedNewsCarousel";

export default function SportsNewsSection({ news, locale, t }: SportsNewsSectionProps) {
  if (!news || news.length === 0) return null;
  return (
    <section className="w-full p-4 sm:p-6 md:p-8 text-white">
      <RelatedNewsCarousel
        news={news}
        locale={locale}
        relatedNewsTitle={t("related_news") || "أخبار ذات صلة"}
        noNewsText={t("no_news") || "لا توجد أخبار"}
      />
    </section>
  );
}
