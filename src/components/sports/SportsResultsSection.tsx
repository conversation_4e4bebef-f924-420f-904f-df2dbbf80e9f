import Image from "next/image";

interface SportsResultsSectionProps {
  matches: any[];
  t: (key: string) => string;
}

export default function SportsResultsSection({ matches, t }: SportsResultsSectionProps) {
  if (!matches || matches.length === 0) return null;
  return (
    <section className="w-full p-4 sm:p-6 md:p-8 text-white">
      <h5 className={"text-xl sm:text-2xl md:text-3xl mb-8"}>{t("latest_results") || "أحدث النتائج"}</h5>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {matches.map((match: any, i: number) => (
          <div key={i} className="bg-[#181818] rounded-lg flex flex-col items-center justify-between border border-[#333] overflow-hidden" style={{ minHeight: 220 }}>
            {/* Date */}
            <div className="w-full bg-[#232323] text-[#DFB254] text-center text-lg font-bold py-2 tracking-widest">{match.date}</div>
            {/* Teams and Score */}
            <div className="flex flex-row items-center justify-center w-full flex-1 py-4 gap-6">
              {/* Home Team */}
              <div className="flex flex-col items-center w-1/3">
                {match.home_logo && (
                  <div className="relative w-[60px] h-[60px] mb-2 flex items-center justify-center flex-shrink-0">
                    <Image src={match.home_logo} alt={match.home_team} fill className="object-contain select-none" />
                  </div>
                )}
                <span className="text-white text-lg font-bold mt-2 text-center min-h-[2.5rem] flex items-center justify-center">{match.home_team}</span>
              </div>
              {/* Score */}
              <div className="flex flex-col items-center w-1/3">
                <span className="text-white text-4xl font-bold">{match.score || "-"}</span>
              </div>
              {/* Away Team */}
              <div className="flex flex-col items-center w-1/3">
                {match.away_logo && (
                  <div className="relative w-[60px] h-[60px] mb-2 flex items-center justify-center flex-shrink-0">
                    <Image src={match.away_logo} alt={match.away_team} fill className="object-contain select-none" />
                  </div>
                )}
                <span className="text-white text-lg font-bold mt-2 text-center min-h-[2.5rem] flex items-center justify-center">{match.away_team}</span>
              </div>
            </div>
            {/* Stadium/Competition */}
            <div className="w-full text-center text-[#DFB254] text-base font-semibold pb-3">
              {match.stadium || match.competition || "-"}
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
