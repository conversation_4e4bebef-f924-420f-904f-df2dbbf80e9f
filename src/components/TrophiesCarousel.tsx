"use client";
import Image from "next/image";
import { useState } from "react";
import translations from "@/lib/locales";

export type Trophy = {
    id: number;
    title: string;
    count: number;
    image?: string | null;
    sport_id?: number;
};

type TrophiesCarouselProps = {
    trophies: Trophy[];
    locale: string;
};

export default function TrophiesCarousel({ trophies, locale }: TrophiesCarouselProps) {
    const [start, setStart] = useState(0);
    const visibleCount = 5;
    const t = (key: string) => translations[locale]?.[key] || key;
    const isRTL = locale === "ar" || locale === "he" || locale === "fa";

    const canGoRight = isRTL ? start > 0 : start + visibleCount < trophies.length;
    const canGoLeft = isRTL ? start + visibleCount < trophies.length : start > 0;

    const handleLeft = () => {
        if (canGoLeft) setStart((prev) => !isRTL ? prev - 1 : prev + 1);
    };
    const handleRight = () => {
        if (canGoRight) setStart((prev) => !isRTL ? prev + 1 : prev - 1);
    };

    // Calculate transform for sliding effect
    const itemWidth = 250 + 24; // max width + gap (px)
    const translate = isRTL ? start * itemWidth : -start * itemWidth;

    return (
        <section className="w-full p-4 sm:p-6 md:p-8 text-white">
            <div className="relative w-full" dir={isRTL ? "rtl" : "ltr"}>
                <div className="flex items-center justify-between mb-4">
                    <h5 className="text-xl sm:text-2xl md:text-3xl">{t("trophies")}</h5>
                    <div className="flex gap-2">
                        <button
                            onClick={handleLeft}
                            disabled={!canGoLeft}
                            className="px-2 py-1 rounded bg-[#DFB254] text-black font-bold disabled:opacity-40"
                            aria-label={isRTL ? t("next") : t("previous")}
                            type="button"
                        >
                            {isRTL ? "→" : "←"}
                        </button>
                        <button
                            onClick={handleRight}
                            disabled={!canGoRight}
                            className="px-2 py-1 rounded bg-[#DFB254] text-black font-bold disabled:opacity-40"
                            aria-label={isRTL ? t("previous") : t("next")}
                            type="button"
                        >
                            {isRTL ? "←" : "→"}
                        </button>
                    </div>
                </div>
                <div className="overflow-hidden w-full">
                    <div
                        className="flex flex-row gap-x-6 pb-4 mx-auto min-h-[340px] transition-transform duration-500"
                        style={{
                            width: itemWidth * trophies.length,
                            transform: `translateX(${translate}px)`,
                            direction: isRTL ? "rtl" : "ltr",
                        }}
                    >
                        {trophies.map((trophy, index) => (
                            <div
                                key={`${trophy.id}-${index}`}
                                className="flex flex-col rounded-sm w-[150px] h-[220px] sm:w-[180px] sm:h-[250px] md:w-[200px] md:h-[280px] lg:w-[250px] lg:h-[325px] overflow-clip drop-shadow-md mx-2 my-4 transition-transform duration-500"
                            >
                                <div className="bg-[#EC2028] min-h-[120px] sm:min-h-[150px] md:min-h-[170px] lg:min-h-[205px] w-full flex justify-center items-center">
                                    {trophy.image ? (
                                        <Image
                                            src={trophy.image}
                                            alt={trophy.title}
                                            width={100}
                                            height={100}
                                            className="w-[60px] h-[60px] sm:w-[80px] sm:h-[80px] md:w-[100px] md:h-[100px] lg:w-auto lg:h-auto"
                                        />
                                    ) : (
                                        <div className="flex items-center justify-center bg-gray-200 text-gray-500 w-[100px] h-[100px] rounded-md">
                                            {t("no_image")}
                                        </div>
                                    )}
                                </div>
                                <div className="bg-[#DB052C] min-h-[100px] sm:min-h-[100px] md:min-h-[110px] lg:min-h-[120px] w-full flex flex-col justify-center items-center border-dashed border-t border-white/50 p-2">
                                    <p className="text-3xl sm:text-4xl md:text-5xl font-semibold">
                                        {trophy.count}
                                    </p>
                                    <p className="text-xs sm:text-sm md:text-base lg:text-lg font-semibold text-center truncate w-full" title={trophy.title}>
                                        {trophy.title}
                                    </p>
                                </div>
                            </div>
                        ))}
                        {trophies.length === 0 && (
                            <div className="text-center text-gray-400 w-full">{t("no_trophies") || "لا توجد بطولات"}</div>
                        )}
                    </div>
                </div>
            </div>
        </section>
    );
}
