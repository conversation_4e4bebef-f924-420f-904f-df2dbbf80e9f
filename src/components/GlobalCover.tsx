import Image from "next/image";

interface GlobalCoverProps {
  image?: string;
  title?: string;
  fallbackImage?: string;
  fallbackTitle?: string;
  overlayClassName?: string;
  containerClassName?: string;
  titleClassName?: string;
}

export default function GlobalCover({
  image,
  title,
  fallbackImage = "/img/default_bg.png",
  fallbackTitle = "",
  overlayClassName = "bg-black/40",
  containerClassName = "relative w-full h-[350px] md:h-[450px] lg:h-[550px] overflow-hidden",
  titleClassName = "text-3xl md:text-4xl font-bold text-white mb-2 drop-shadow-lg",
}: GlobalCoverProps) {
  return (
    <div className={containerClassName}>
      <Image
        src={image || fallbackImage}
        alt={title || fallbackTitle || "Cover"}
        fill
        className="object-cover object-center"
        priority
      />
      <div className={`absolute inset-0 ${overlayClassName}`} />
      <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
        {title && (
          <h1 className={titleClassName}>{title}</h1>
        )}
      </div>
    </div>
  );
}
