import Link from 'next/link';
import Image from 'next/image';

interface RelatedNewsWidgetProps {
  related: any[];
  t: (key: string) => string;
  locale: string;
}

export default function RelatedNewsWidget({ related, t, locale, title }: RelatedNewsWidgetProps & { title?: string }) {
  return (
    <div className="bg-white rounded shadow p-4 mb-6">
      <h3 className="font-bold text-lg mb-3 text-black">{title || t('related_news')}</h3>
      <ul className="space-y-3">
        {related && related.length > 0 ? (
          related.map((item: any) => (
            <li key={item.id}>
              <Link
                href={`/${locale}/news/article/${item.slug}`}
                className="flex gap-3 items-center hover:bg-gray-50 rounded p-2 transition"
              >
                <div className="w-16 h-12 relative flex-shrink-0">
                  <Image src={item.image} alt={item.title} fill className="object-cover rounded" />
                </div>
                <div className="flex-1">
                  <div className="font-semibold text-sm line-clamp-2 text-black">{item.title}</div>
                  {item.published_at && (
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(item.published_at).toLocaleDateString(
                        locale.replace('_', '-'),
                        { day: 'numeric', month: 'long', year: 'numeric' }
                      )}
                    </div>
                  )}
                </div>
              </Link>
            </li>
          ))
        ) : (
          <li className="text-gray-400">{t('no_related_news')}</li>
        )}
      </ul>
    </div>
  );
}
