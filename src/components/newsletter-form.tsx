"use client";
import { useState } from "react";
import { fetchApi } from '@/lib/api';

export default function NewsletterForm({ tDict, locale }: { tDict: Record<string, string>, locale: string }) {
    const t = (key: string) => tDict?.[key] || key;

    const [email, setEmail] = useState("");
    const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>("idle");
    const [message, setMessage] = useState("");

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setStatus("loading");
        setMessage("");
        try {
            const formData = new URLSearchParams();
            formData.append('email', email);
            const res = await fetchApi("/api/general/newsletter/subscribe", {
                method: "POST",
                headers: { "Content-Type": "application/x-www-form-urlencoded", "X-localization": locale },
                body: formData.toString(),
            });
            let debugData;
            try {
                debugData = await res.clone().json();
                // eslint-disable-next-line no-console
                console.log('Newsletter subscribe response:', debugData);
            } catch { /* ignore */ }
            if ((res.status === 200 || res.status === 201) && email) {
                let data = debugData;
                setStatus("success");
                setMessage((data && typeof data.message === 'string' && data.message.trim()) ? data.message : t("newsletter_success"));
                setEmail("");
            } else {
                let data = debugData;
                let errorMsg = t("newsletter_error");
                if (data?.errors?.email?.length) {
                    errorMsg = data.errors.email[0];
                } else if (data?.message) {
                    errorMsg = data.message;
                }
                setStatus("error");
                setMessage(errorMsg);
            }
        } catch {
            setStatus("error");
            setMessage(t("newsletter_error"));
        }
    };

    return (
        <form className="flex flex-col gap-2" onSubmit={handleSubmit}>
            <input
                type="email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                placeholder={t("newsletter_email_placeholder")}
                className="border rounded px-3 py-2 text-sm"
                required
            />
            <button type="submit" className="bg-[#EC2028] text-white rounded py-2 font-bold" disabled={status === "loading"}>
                {status === "loading" ? t("subscribing") : t("subscribe")}
            </button>
            {message && <div className={`text-xs mt-1 ${status === "success" ? "text-green-600" : "text-red-600"}`}>{message}</div>}
        </form>
    );
}
