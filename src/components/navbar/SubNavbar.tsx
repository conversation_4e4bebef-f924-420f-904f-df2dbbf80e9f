import Link from "next/link";

export default function SubNavbar({ activeSubNav, navbarData, locale, pathname, translations, sports, handleNavLinkClick }: any) {
  if (activeSubNav === null) return null;
  const activeItem = navbarData.bottom[activeSubNav];
  if (activeItem.label === "sports" && sports.length > 0) {
    return (
      <div className="hidden lg:flex w-full h-[50px] px-4 lg:px-20 justify-end items-center" style={{ background: 'linear-gradient(-145deg, #DFB254 0%, #C60428 35%, #7A0515 70%, #0A0A0A 100%)' }}>
        {sports.map((sport: any, index: number) => {
          const urlWithLocale = `/${locale}/sports/${sport.slug}`;
          const isActive = pathname === urlWithLocale || pathname === `${urlWithLocale}/`;
          return (
            <Link
              key={`subnav_${index}`}
              href={urlWithLocale}
              className={`px-2 py-3 text-sm truncate overflow-hidden whitespace-nowrap hover:bg-[#050709] transition-colors duration-300 z-50 border-2 border-transparent hover:border-b-[#DFB254]${isActive ? ' bg-[#050709] border-b-[#DFB254] font-bold text-[#DFB254]' : ''}`}
              onClick={() => handleNavLinkClick(sport.slug)}
            >
              {sport.title}
            </Link>
          );
        })}
      </div>
    );
  }
  if (!activeItem.sublinks) return null;
  const endActive = activeItem.label === "club" ||  activeItem.label === "media_and_videos"||  activeItem.label === "alahly_magazine";
  return (
    <div className={`hidden lg:flex w-full h-[36px] px-2 lg:px-20 ${endActive ? 'justify-end' : 'justify-center'} items-center`} style={{ background: 'linear-gradient(-145deg, #DFB254 0%, #C60428 35%, #7A0515 70%, #0A0A0A 100%)' }}>
      {activeItem.sublinks.map((link: any, index: number) => {
        const isExternal = link.url && (link.url.startsWith('http://') || link.url.startsWith('https://'));
        const urlWithLocale = !isExternal && link.url && link.url.startsWith('/') ? `/${locale}${link.url}` : link.url;
        const isActive = !isExternal && (pathname === urlWithLocale || pathname === `${urlWithLocale}/` || (pathname && pathname.startsWith(urlWithLocale + "/")));
        if (isExternal) {
          return (
            <a
              key={`subnav_${index}`}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className={`px-2 py-1 text-sm truncate overflow-hidden whitespace-nowrap hover:bg-[#050709] transition-colors duration-300 z-50 border-2 border-transparent hover:border-b-[#DFB254]`}
              style={{ maxWidth: '160px' }}
            >
              {translations[locale]?.[link.label] || link.label}
            </a>
          );
        }
        if (link.label === "download_pdf") {
          return (
            <a
              key={`subnav_${index}`}
              href={urlWithLocale}
              target="_blank"
              rel="noopener noreferrer"
              className={`px-2 py-1 text-sm truncate overflow-hidden whitespace-nowrap hover:bg-[#050709] transition-colors duration-300 z-50 border-2 border-transparent hover:border-b-[#DFB254]${isActive ? ' bg-[#050709] border-b-[#DFB254] font-bold text-[#DFB254]' : ''}`}
              style={{ maxWidth: '160px' }}
            >
              {translations[locale]?.[link.label] || link.label}
            </a>
          );
        }
        return (
          <Link
            key={`subnav_${index}`}
            href={urlWithLocale}
            className={`px-2 py-1 text-sm truncate overflow-hidden whitespace-nowrap hover:bg-[#050709] transition-colors duration-300 z-50 border-2 border-transparent hover:border-b-[#DFB254]${isActive ? ' bg-[#050709] border-b-[#DFB254] font-bold text-[#DFB254]' : ''}`}
            style={{ maxWidth: '160px' }}
            onClick={() => handleNavLinkClick(link.url)}
          >
            {translations[locale]?.[link.label] || link.label}
          </Link>
        );
      })}
    </div>
  );
}
