import Link from "next/link";
import Image from "next/image";

export default function MobileTopLinks({ links, locale, translations, handleNavLinkClick, setIsMobileMenuOpen, setIsSearchModalOpen }: any) {
  return (
    <div className="grid grid-cols-3 gap-3 mb-6">
      {links.map((link: any, index: number) => {
        // Handle external links (https)
        if (link.url && (link.url.startsWith('http://') || link.url.startsWith('https://'))) {
          return (
            <a
              key={`mobile_top_${index}`}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex flex-col items-center justify-center px-2 py-4 rounded-lg hover:bg-[#a00320] transition-colors duration-300 text-base font-semibold text-white text-center shadow-sm active:scale-95 min-h-[80px]"
            >
              {link.icon && (
                <Image
                  src={link.icon}
                  alt={link.label}
                  width={28}
                  height={28}
                  className="mb-2 w-7 h-7"
                />
              )}
              <span className="truncate w-full block text-xs sm:text-sm mt-1">
                {translations[locale]?.[link.label] || link.label}
              </span>
            </a>
          );
        }

        return link.url ? (
          <Link
            key={`mobile_top_${index}`}
            href={`/${locale}${link.url}`}
            className="flex flex-col items-center justify-center px-2 py-4 rounded-lg hover:bg-[#a00320] transition-colors duration-300 text-base font-semibold text-white text-center shadow-sm active:scale-95 min-h-[80px]"
            onClick={() => { handleNavLinkClick(link.url); setIsMobileMenuOpen(false); }}
          >
            {link.icon && (
              <Image
                src={link.icon}
                alt={link.label}
                width={28}
                height={28}
                className="mb-2 w-7 h-7"
              />
            )}
            <span className="truncate w-full block text-xs sm:text-sm mt-1">
              {translations[locale]?.[link.label] || link.label}
            </span>
          </Link>
        ) : (
          <span
            key={`mobile_top_${index}`}
            className="flex flex-col items-center justify-center px-2 py-4 rounded-lg bg-[#a00320] text-base font-semibold text-white text-center shadow-sm opacity-60 cursor-not-allowed min-h-[80px]"
          >
            {link.icon && (
              <Image
                src={link.icon}
                alt={link.label}
                width={28}
                height={28}
                className="mb-2 w-7 h-7"
              />
            )}
            <span className="truncate w-full block text-xs sm:text-sm mt-1">
              {translations[locale]?.[link.label] || link.label}
            </span>
          </span>
        );
      })}
      {/* Search Button as third grid item */}
      <button
        type="button"
        className="flex flex-col items-center justify-center px-2 py-4 rounded-lg hover:bg-[#a00320] transition-colors duration-300 text-base font-semibold text-white text-center shadow-sm active:scale-95 min-h-[80px]"
        onClick={() => setIsSearchModalOpen(true)}
        title={translations[locale]?.['search'] || 'Search'}
      >
        <Image
          src="/search_icon.svg"
          alt={translations[locale]?.['search'] || 'Search'}
          width={28}
          height={28}
          className="mb-2 w-7 h-7"
        />
        <span className="truncate w-full block text-xs sm:text-sm mt-1">
          {translations[locale]?.['search'] || 'Search'}
        </span>
      </button>
    </div>
  );
}
