"use client";
import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import SearchModal from "../SearchModal";

export default function BottomLinks({ navbarData, locale, translations, pathname, activeSubNav, toggleSubNav, handleNavLinkClick }: any) {
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  
  return (
    <>
      {navbarData.bottom.map((item: any, index: number) => {
    if (!item.sublinks && item.url) {
      const itemPath = `/${locale}${item.url}`;
      const currentPath = pathname || "";
      const isActive = currentPath === itemPath || currentPath === `${itemPath}/` || currentPath.startsWith(itemPath + "/");
      return (
        <Link
          key={`bottom_${index}`}
          href={itemPath}
          className={`px-2 py-1 text-base truncate overflow-hidden whitespace-nowrap transition-colors duration-300 z-50 cursor-pointer border-2 border-transparent hover:border-b-[#DFB254] hover:bg-[#EC2028]${isActive ? ' bg-[#ec2028] border-b-[#DFB254]' : ''}`}
          onClick={() => handleNavLinkClick(item.url)}
        >
          {translations[locale]?.[item.label] || item.label}
        </Link>
      );
    }
    const currentPath = pathname || "";
    const hasActiveSublink = item.sublinks && item.sublinks.some((link: any) => {
      return currentPath === link.url || currentPath === `${link.url}/` || currentPath.startsWith(link.url + "/");
    });
    return (
      <button
        key={`bottom_${index}`}
        onClick={() => toggleSubNav(index)}
        className={`px-3 py-1 text-base truncate overflow-hidden whitespace-nowrap transition-colors duration-300 z-50 cursor-pointer border-2 border-transparent hover:border-b-[#DFB254] ${activeSubNav === index ? "bg-[#EC2028]" : "hover:bg-[#EC2028]"}${hasActiveSublink ? ' bg-[#EC2028] border-b-[#DFB254] font-bold text-[#DFB254]' : ''}`}
      >
        {translations[locale]?.[item.label] || item.label}
      </button>
    );
  })}
  
  {/* Search Button */}
  <button
    onClick={() => setIsSearchModalOpen(true)}
    className="flex items-center justify-center px-3 py-1 transition-colors duration-300 hover:bg-[#EC2028] border-2 border-transparent hover:border-b-[#DFB254] z-50"
    title={translations[locale]?.['search'] || 'Search'}
  >
    <Image
      src="/search_icon.svg"
      alt={translations[locale]?.['search'] || 'Search'}
      width={20}
      height={20}
      className="w-5 h-5"
    />
  </button>
  
  <SearchModal
    isOpen={isSearchModalOpen}
    onClose={() => setIsSearchModalOpen(false)}
    locale={locale}
    translations={translations}
  />
  </>
  );
}
