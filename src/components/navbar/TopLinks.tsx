"use client";
import Link from "next/link";
import Image from "next/image";

export default function TopLinks({ links, locale, translations, user }: any) {

  return (
    <>
      {links.map((link: any, index: number) => {
        if (link.label.toLowerCase() === "registration" && user) {
          return null;
        }
        if (link.label.toLowerCase() === "login_form_submit" && user) {
          return null;
        }
        // Handle external links (https)
        if (link.url && (link.url.startsWith('http://') || link.url.startsWith('https://'))) {
          return (
            <a
              key={`top_${index}`}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 px-2 py-1 transition-colors duration-300 hover:bg-[#d11a2a] border-2 border-transparent hover:border-b-[#DFB254] z-50"
            >
              {link.icon && (
                <Image
                  src={link.icon}
                  alt={link.label}
                  width={20}
                  height={20}
                  className="w-5 h-5"
                />
              )}
              <span>{translations[locale]?.[link.label] || link.label}</span>
            </a>
          );
        }
        // Ensure no double locale prefix for internal links
        let href = link.url.startsWith('/') ? `/${locale}${link.url}` : link.url;
        return (
          <Link
            key={`top_${index}`}
            href={href}
            className="flex items-center gap-2 px-2 py-1 transition-colors duration-300 hover:bg-[#d11a2a] border-2 border-transparent hover:border-b-[#DFB254] z-50"
          >
            {link.icon && (
              <Image
                src={link.icon}
                alt={link.label}
                width={20}
                height={20}
                className="w-5 h-5"
              />
            )}
            <span>{translations[locale]?.[link.label] || link.label}</span>
          </Link>
        );
      })}
    </>
  );
}
