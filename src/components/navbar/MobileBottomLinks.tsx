import Link from "next/link";

export default function MobileBottomLinks({ navbarData, locale, translations, sports, activeSubNav, toggleSubNav, handleNavLinkClick, setIsMobileMenuOpen }: any) {
  return (
    <div className="flex flex-col space-y-4 mt-6">
      {navbarData.bottom.map((item: any, index: number) => {
        const hasSubmenu = item.label === "sports" ? sports.length > 0 : Array.isArray(item.sublinks) && item.sublinks.length > 0;
        // Handle items with only a URL and no sublinks (no subnav)
        if (!hasSubmenu && item.url) {
          // Support external links
          const isExternal = item.url.startsWith('http://') || item.url.startsWith('https://');
          const itemPath = isExternal ? item.url : `/${locale}${item.url}`;
          if (isExternal) {
            return (
              <a
                key={`mobile_bottom_${index}`}
                href={itemPath}
                target="_blank"
                rel="noopener noreferrer"
                className="px-4 py-3 rounded-lg hover:bg-[#a00320] transition-colors duration-300 text-lg font-semibold text-white shadow-sm active:scale-95"
              >
                {translations[locale]?.[item.label] || item.label}
              </a>
            );
          }
          return (
            <Link
              key={`mobile_bottom_${index}`}
              href={itemPath}
              className="px-4 py-3 rounded-lg hover:bg-[#a00320] transition-colors duration-300 text-lg font-semibold text-white shadow-sm active:scale-95"
              onClick={() => { handleNavLinkClick(item.url); setIsMobileMenuOpen(false); }}
            >
              {translations[locale]?.[item.label] || item.label}
            </Link>
          );
        }
        if (item.label === "sports") {
          return (
            <div key={`mobile_bottom_${index}`} className="flex flex-col">
              <button
                onClick={() => toggleSubNav(index)}
                className={`px-4 py-3 text-left rounded-lg font-semibold text-white flex items-center justify-between ${activeSubNav === index ? "bg-[#80021a]" : "hover:bg-[#a00320]"} transition-colors duration-300`}
              >
                <span>{translations[locale]?.[item.label] || item.label}</span>
                {hasSubmenu && (
                  <span className={`ml-2 transition-transform duration-300 ${activeSubNav === index ? 'rotate-90' : ''}`}>▶</span>
                )}
              </button>
              {activeSubNav === index && sports.length > 0 && (
                <div className="bg-[#80021a] rounded-b-xl mt-1 animate-fadeIn">
                  {sports.map((sport: any) => (
                    <Link
                      key={sport.id}
                      href={`/${locale}/sports/${sport.slug}`}
                      className="block px-8 py-3 hover:bg-[#600115] transition-colors duration-300 text-white text-center rounded-lg active:scale-95"
                      onClick={() => { handleNavLinkClick(`/sports/${sport.slug}`); setIsMobileMenuOpen(false); }}
                    >
                      {sport.title}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          );
        }
        return (
          <div key={`mobile_bottom_${index}`} className="flex flex-col">
            <button
              onClick={() => toggleSubNav(index)}
              className={`px-4 py-3 text-left rounded-lg font-semibold text-white flex items-center justify-between ${activeSubNav === index ? "bg-[#80021a]" : "hover:bg-[#a00320]"} transition-colors duration-300`}
            >
              <span>{translations[locale]?.[item.label] || item.label}</span>
              {hasSubmenu && (
                <span className={`ml-2 transition-transform duration-300 ${activeSubNav === index ? 'rotate-90' : ''}`}>▶</span>
              )}
            </button>
            {activeSubNav === index && item.sublinks && (
              <div className="bg-[#80021a] rounded-b-xl mt-1 animate-fadeIn">
                {item.sublinks.map((link: any, subIndex: number) => {
                  const isExternal = link.url && (link.url.startsWith('http://') || link.url.startsWith('https://'));
                  const sublinkPath = !isExternal && link.url && link.url.startsWith('/') ? `/${locale}${link.url}` : link.url;
                  if (isExternal) {
                    return (
                      <a
                        key={`mobile_sub_${index}_${subIndex}`}
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block px-8 py-3 hover:bg-[#600115] transition-colors duration-300 text-white text-center rounded-lg active:scale-95"
                      >
                        {translations[locale]?.[link.label] || link.label}
                      </a>
                    );
                  }
                  return (
                    <Link
                      key={`mobile_sub_${index}_${subIndex}`}
                      href={sublinkPath}
                      className="block px-8 py-3 hover:bg-[#600115] transition-colors duration-300 text-white text-center rounded-lg active:scale-95"
                      onClick={() => { handleNavLinkClick(link.url); setIsMobileMenuOpen(false); }}
                    >
                      {translations[locale]?.[link.label] || link.label}
                    </Link>
                  );
                })}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
