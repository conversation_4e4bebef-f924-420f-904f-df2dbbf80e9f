"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { fetchApi } from "@/lib/api";
import { usePathname } from "next/navigation";
import translations from "@/lib/locales";

export type NewsItem = {
  src: string;
  title: string;
  intro: string;
  slug: string;
  published_at?: string;
};

export default function NewsSection({ news, categories: initialCategories, leagueTable, leaugeTitle }: { news: NewsItem[], categories: any[], leagueTable?: any[], leaugeTitle?: string }) {
  const [categories, setCategories] = useState<any[]>(initialCategories || []);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [categoryNews, setCategoryNews] = useState<NewsItem[] | null>(null);
  const [loading, setLoading] = useState(false);
  const pathname = usePathname();
  const locale = pathname ? pathname.split("/")[1] : "ar";

  const t = (key: string) => translations[locale]?.[key] || key;

  // Set active category to first one on mount if not set
  useEffect(() => {
    if (categories.length > 0 && !activeCategory) {
      setActiveCategory(categories[0].slug);
    }
  }, [categories, activeCategory]);

  // Sync categories state with prop changes
  useEffect(() => {
    setCategories(initialCategories || []);
  }, [initialCategories]);

  const handleCategoryClick = async (slug: string) => {
    setActiveCategory(slug);
    setLoading(true);
    try {
      const data = await fetchApi(`/api/news/category/${slug}?limit=5`, {
        headers: { "X-localization": locale },
      });
      setCategoryNews((data.news || []).map((item: any) => ({
        src: item.image_full,
        title: item.title,
        intro: item.intro,
        slug: item.slug,
        published_at: item.published_at,
      })));
    } catch {
      setCategoryNews([]);
    }
    setLoading(false);
  };

  const NewsCard = ({ src, title, intro, slug, published_at }: { src: string; title: string; intro: string; slug: string; published_at: string }) => (
    <Link href={`/${locale}/news/article/${slug}`} className="cursor-pointer group h-full flex flex-col">
      <div className="relative overflow-hidden flex-grow">
        {/* Image container that shrinks to 60% height on hover */}
        <div className="h-full group-hover:h-[60%] transition-all duration-500 ease-in-out overflow-hidden">
          <img
            src={src}
            alt={title}
            className="w-full h-full object-cover"
            loading="lazy"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end p-2 sm:p-3 md:p-4">
            <h3 className="text-white font-bold text-sm sm:text-xl md:text-xl lg:text-2xl leading-tight line-clamp-2 w-full break-words">
              {title}
            </h3>
          </div>
        </div>

        {/* Text content that appears below the image on hover */}
        <div className="absolute bottom-0 left-0 right-0 h-0 group-hover:h-[40%] transition-all duration-500 ease-in-out bg-white overflow-hidden">
          <div className="p-3 sm:p-6 md:p-8">
            <p className="text-[#B4892F] font-bold text-sm sm:text-xl md:text-xl lg:text-2xl leading-tight line-clamp-2 w-full break-words mb-3 ps-4 pe-4 group-hover:ps-1 group-hover:pe-1">
              <span className="bg-[#EC2028] px-0.5 py-0 m-1 sm:m-1.5"></span>
              {title}
            </p>
            <p className="text-black/90 text-xs sm:text-sm md:text-sm lg:text-base font-normal line-clamp-2 w-full break-words ps-4 pe-4 group-hover:ps-1 group-hover:pe-1">
              {intro}
            </p>
            <div className="mt-1 sm:mt-2 text-[10px] sm:text-xs text-gray-400 text-left ps-4 pe-4 group-hover:ps-1 group-hover:pe-1">
              {new Date(published_at).toLocaleString(
                locale.replace('_', '-'),
                { day: 'numeric', month: 'long', year: 'numeric', hour: '2-digit', minute: '2-digit' }
              )}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );

  return (
    <div className="w-full px-2 sm:px-4 md:px-6 lg:px-0">
      <div className="flex flex-col lg:flex-row gap-3 sm:gap-4">
        {/* League Table Section - only render if leagueTable exists and has data */}
        {leagueTable && leagueTable.length > 0 && (
          <div className="min-w-[350px] h-auto rounded bg-white drop-shadow-md">
            <div className="bg-[#474343] flex justify-between items-center py-3 px-4">
              <span className="text-xl font-semibold">{leaugeTitle || t("league_table")}</span>
            </div>
            <div className="flex flex-col gap-2 p-2">
              <div className="w-full h-10 flex items-center text-[#A5A5A5] text-sm font-semibold">
                <span className="flex-1 min-w-[90px] truncate text-start pr-2">{t("club")}</span>
                <span className="w-10 text-center">{t("played")}</span>
                <span className="w-10 text-center">{t("win")}</span>
                <span className="w-10 text-center">{t("draw")}</span>
                <span className="w-10 text-center">{t("lose")}</span>
                <span className="w-12 text-center">{t("points")}</span>
              </div>
              {leagueTable.map((row, i) => (
                <div
                  key={row.id || i}
                  className={`w-full h-10 flex items-center px-2 ${i % 2 === 0 ? "bg-[#F1F0F0]" : "bg-[#E9E9E9]"} rounded-sm`}
                >
                  <span className="flex-1 min-w-[90px] truncate text-start pr-2 font-semibold text-black" title={row.club}>{row.club}</span>
                  <span className="w-10 text-center text-[#474343] font-bold">{row.played}</span>
                  <span className="w-10 text-center text-[#1A7A1F] font-bold">{row.win}</span>
                  <span className="w-10 text-center text-[#B4892F] font-bold">{row.draw}</span>
                  <span className="w-10 text-center text-[#C60428] font-bold">{row.lose}</span>
                  <span className="w-12 text-center font-bold text-[#C60428]">{row.points}</span>
                </div>
              ))}
            </div>
          </div>
        )}
        <div className="grow">
          <div className="flex flex-col gap-2 sm:gap-3 md:gap-4 lg:gap-5 p-2 sm:p-3 md:p-4 lg:p-5">
            <div className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-black border-s-2 sm:border-s-3 md:border-s-4 lg:border-s-6 border-[#D10128] ps-2 sm:ps-3 py-1 sm:py-2 md:py-3 flex items-center justify-between">
              {t("news")}
              <Link
                href={`/${locale}/news`}
                className="px-2 py-1 sm:px-3 sm:py-2 md:px-4 md:py-2 lg:px-6 lg:py-3 bg-[#EC2028] rounded-xs cursor-pointer font-semibold text-white text-xs sm:text-sm md:text-base whitespace-nowrap"
              >
                {t("more")}
              </Link>
            </div>
            <div className="flex flex-wrap items-center gap-1 sm:gap-2 md:gap-3 overflow-x-auto pb-1 sm:pb-2 md:pb-0">
              <div className="w-full overflow-x-auto">
                <div className="flex items-center gap-1 sm:gap-2 md:gap-3 min-w-fit w-max">
                  {categories && categories.length > 0 && categories.map((category, index) => (
                    <button
                      key={category.id}
                      onClick={() => handleCategoryClick(category.slug)}
                      className={`px-2 py-1 sm:px-3 sm:py-2 md:px-4 md:py-2 lg:px-6 lg:py-3 rounded-xs cursor-pointer font-semibold whitespace-nowrap transition-all duration-300
          ${activeCategory === category.slug
                          ? 'bg-[#EC2028] text-white text-xs sm:text-sm md:text-base max-w-[90px] sm:max-w-[120px] font-bold shadow-lg'
                          : 'bg-[#EDEDED] text-[#515151] text-sm sm:text-base md:text-lg max-w-[180px] md:max-w-[220px] truncate opacity-90 hover:opacity-100'}
        `}
                      style={{ direction: 'rtl', textAlign: activeCategory === category.slug ? 'right' : 'center' }}
                    >
                      {category.title}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
          {/* News grid from API data */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 sm:gap-3 md:gap-4 p-2 sm:p-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="animate-pulse bg-gray-200 rounded-lg h-48 w-full flex flex-col">
                  <div className="h-2/3 bg-gray-300 rounded-t-lg w-full" />
                  <div className="p-4 flex-1 flex flex-col gap-2">
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-2" />
                    <div className="h-3 bg-gray-300 rounded w-1/2 mb-1" />
                    <div className="h-3 bg-gray-200 rounded w-1/3" />
                  </div>
                </div>
              ))}
            </div>
          ) : (categoryNews && categoryNews.length === 0) || (!categoryNews && news.length === 0) ? (
            <div className="w-full flex justify-center items-center py-8">
              <span className="text-gray-400 text-lg font-semibold">{t("no_news")}</span>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 sm:gap-3 md:gap-4 p-2 sm:p-3">
                {(categoryNews || news).slice(0, 2).map((item, idx) => (
                  <NewsCard key={idx} src={item.src} title={item.title} intro={item.intro} slug={item.slug} published_at={item.published_at || ''} />
                ))}
                <div className="col-span-1 md:col-span-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3 md:gap-4">
                  {(categoryNews || news).slice(2, 5).map((item, idx) => (
                    <NewsCard key={idx} src={item.src} title={item.title} intro={item.intro} slug={item.slug} published_at={item.published_at || ''} />
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
