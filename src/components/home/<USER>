"use client";

import Image from "next/image";
import { usePathname } from "next/navigation";
import translations from "@/lib/locales";
import Link from "next/link";

function useTranslations() {
  const pathname = usePathname();
  const locale = pathname ? pathname.split("/")[1] : "ar";
  return (key: string) => translations[locale]?.[key] || key;
}

export type Trophy = {
  id: number;
  title: string;
  count: number;
  image?: string | null;
  sport_id?: number;
};

export type TrophiesData = {
  total_trophies?: string | number;
  trophies: Trophy[];
};

export default function TrophiesSection({ trophiesData }: { trophiesData: TrophiesData }) {
  const t = useTranslations();

  // Guard: if trophiesData or trophiesData.trophies is missing or not an array, show fallback UI
  if (!trophiesData || !Array.isArray(trophiesData.trophies) || trophiesData.trophies.length === 0) {
    return (
      <div className="w-full flex items-center justify-center py-12 text-gray-500 text-lg md:text-2xl font-semibold">
        {t("no_trophies")}
      </div>
    );
  }

  const renderTrophies = () => {
    return trophiesData.trophies.map((trophy, index) => (
      <div
        key={`${trophy.id}-${index}`}
        className="flex flex-col rounded-sm w-[150px] h-[220px] sm:w-[180px] sm:h-[250px] md:w-[200px] md:h-[280px] lg:w-[250px] lg:h-[325px] overflow-clip drop-shadow-md mx-2 my-4"
      >
        <div className="bg-[#EC2028] min-h-[120px] sm:min-h-[150px] md:min-h-[170px] lg:min-h-[205px] w-full flex justify-center items-center">
          {trophy.image ? (
            <Image
              src={trophy.image}
              alt={trophy.title}
              width={100}
              height={100}
              className="w-[60px] h-[60px] sm:w-[80px] sm:h-[80px] md:w-[100px] md:h-[100px] lg:w-auto lg:h-auto"
            />
          ) : (
            <div className="flex items-center justify-center bg-gray-200 text-gray-500 w-[100px] h-[100px] rounded-md">
              {t("no_image")}
            </div>
          )}
        </div>
        <div className="bg-[#DB052C] min-h-[100px] sm:min-h-[100px] md:min-h-[110px] lg:min-h-[120px] w-full flex flex-col justify-center items-center border-dashed border-t border-white/50 p-2">
          <p key="count" className="text-3xl sm:text-4xl md:text-5xl font-semibold">
            {trophy.count}
          </p>
          <p key="name" className="text-xs sm:text-sm md:text-base lg:text-lg font-semibold text-center truncate w-full" title={trophy.title}>
            {trophy.title}
          </p>
        </div>
      </div>
    ));
  };

  return (
    <div className="w-full bg-[#C60428] p-4 sm:p-6 md:p-8 px-4 py-12 lg:py-18 mb-2">
      <div className="flex flex-col md:flex-row justify-between items-center md:items-start">
        <div className="flex gap-3">
          <span className="h-5 w-5 bg-[#DEB65D] mt-1"></span>
          <div className="flex flex-col gap-1 items-start">
            <h5 className="text-xl sm:text-2xl md:text-3xl">{t("football")}</h5>
            <h5 className="text-2xl sm:text-3xl md:text-4xl font-bold">
              {t("trophy_cabinet")}
            </h5>
          </div>
        </div>
        <div className="mt-4 md:mt-0 md:ml-14 flex flex-col items-center md:items-end">
          <h5 className="text-[3em] sm:text-[5em] md:text-[7em] font-bold leading-none">
            {trophiesData.total_trophies}
          </h5>
          <h5 className="text-xl sm:text-2xl md:text-3xl font-semibold">
            {t("championships_and_titles")}
          </h5>
        </div>
      </div>
      <div className="flex justify-center flex-wrap mt-6 sm:mt-8 md:mt-10 mx-auto max-w-[1800px]">
        {renderTrophies()}
      </div>
      <div className="w-full flex justify-center mt-4 sm:mt-6">
        <Link
          href={`/${typeof window !== 'undefined' ? window.location.pathname.split('/')[1] : 'ar'}/football/achievements`}
          className="px-4 py-2 sm:px-6 sm:py-3 bg-[#EC2028] rounded-xs cursor-pointer font-semibold text-sm sm:text-base text-white text-center"
        >
          {t("all_trophies")}
        </Link>
      </div>
    </div>
  );
}
