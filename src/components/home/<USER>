"use client";

import Image from "next/image";
import { useState, useEffect } from "react";
import translations from "@/lib/locales";
import { usePathname } from "next/navigation";

export type Player = {
  id: number;
  slug: string;
  name: string;
  image?: string | null; // allow undefined/null
  position?: string; // optional position field
};

export default function TeamSection({ players }: { players: Player[] }) {
  const [activeIndex, setActiveIndex] = useState(
    Math.floor(players.length / 2)
  );
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isClient, setIsClient] = useState(false);

  const pathname = usePathname();
  const locale = pathname ? pathname.split("/")[1] : "ar";
  const t = (key: string) => translations[locale]?.[key] || key;

  useEffect(() => {
    setIsClient(true);
  }, []);

  const [windowWidth, setWindowWidth] = useState(0);

  useEffect(() => {
    setWindowWidth(window.innerWidth);

    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const next = () => {
    setActiveIndex((prev) => (prev + 1) % players.length);
    setIsAutoPlaying(false);
  };

  const prev = () => {
    setActiveIndex((prev) => (prev - 1 + players.length) % players.length);
    setIsAutoPlaying(false);
  };

  const getStyleForPlayer = (index: number) => {
    let diff = index - activeIndex;
    const half = Math.floor(players.length / 2);

    if (diff > half) diff -= players.length;
    if (diff < -half) diff += players.length;

    const distance = Math.abs(diff);

    let scale = 1;
    let opacity = 1;
    let zIndex = 30;

    let translateX = diff * (windowWidth >= 768 ? 300 : 140);

    if (index === activeIndex) {
      scale = windowWidth >= 768 ? 1.25 : 1.15;
      zIndex = 99; // Make the active one the highest z-index
      opacity = 1;
      translateX = 0;
    }

    return {
      position: "absolute" as const,
      left: "50%",
      top: "50%",
      transform: `translateX(calc(${translateX}px - 50%)) translateY(-50%) scale(${scale})`,
      opacity,
      zIndex,
      transition: "transform 0.5s ease, opacity 0.5s ease",
      transformOrigin: "center center",
      width: windowWidth >= 768 ? "240px" : "180px",
      display: opacity === 0 ? "none" : "flex",
      flexDirection: "column" as const,
      alignItems: "center" as const,
      justifyContent: "flex-end" as const,
    };
  };

  useEffect(() => {
    if (!isAutoPlaying) return;
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % players.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, players.length]);

  return (
    <div className="w-full bg-[#333333] bg-[url(/img/smoke-bg.png)] bg-cover relative">
      <img
        src={"/img/player-bg.png"}
        alt={t("player_bg_alt")}
        className="absolute h-full w-full object-cover left-0 filter grayscale opacity-80"
      />
      <div className="flex gap-3 p-8 relative z-10">
        <span className="h-5 w-5 bg-[#DEB65D]"></span>
        <div className="flex flex-col gap-1 pt-3">
          <h5 className="text-2xl md:text-3xl text-gray-300">
            {t("football")}
          </h5>
          <h5 className="text-3xl md:text-4xl font-bold">{t("first_team")}</h5>
        </div>
      </div>
      <div className="relative w-full h-[450px] md:h-[550px] mx-auto mt-8 md:mt-10 overflow-hidden">
        <div className="relative w-full h-full">
          {players.map((player, index) => {
            const styles = getStyleForPlayer(index);
            return (
              <div key={`${player.id}-${index}`} style={styles}>
                {player.image ? (
                  <Image
                    src={player.image}
                    alt={player.name}
                    width={240}
                    height={320}
                    className="object-cover rounded-md h-[240px] w-[180px] md:h-[320px] md:w-[240px]"
                  />
                ) : (
                  <div className="flex items-center justify-center bg-gray-200 text-gray-500 h-[240px] w-[180px] md:h-[320px] md:w-[240px] rounded-md">
                    {t("no_image")}
                  </div>
                )}
                <div
                  className={`mt-3 text-center transition-opacity duration-300 ${index === activeIndex ? "opacity-100" : "opacity-0"
                    }`}
                >
                  <p className="text-sm md:text-lg font-bold text-[#DEB65D]">
                    {player.name}
                  </p>
                  <p className="text-xs md:text-sm">{player.position}</p>
                </div>
              </div>
            );
          })}
        </div>

        <button
          onClick={locale === 'ar' ? next : prev}
          className="absolute cursor-pointer right-[10%] bottom-10 md:right-[25%] md:bottom-12 z-40 text-white px-3 py-1 md:px-4 md:py-2 rounded-lg hover:text-black transition-colors duration-300 text-3xl"
        >
          {locale === 'ar' ? '❮' : '❯'}
        </button>

        <button
          onClick={locale === 'ar' ? prev : next}
          className="absolute cursor-pointer left-[10%] bottom-10 md:left-[25%] md:bottom-12 z-40 text-white px-3 py-1 md:px-4 md:py-2 rounded-lg hover:text-black transition-colors duration-300 text-3xl"
        >
          {locale === 'ar' ? '❯' : '❮'}
        </button>
      </div>
    </div>
  );
}
