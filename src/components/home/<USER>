"use client";
import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import SocialCard from "../social-card";
import Image from "next/image";
import React from "react";

export type SocialPost = {
  socialIcon: string;
  profilePicture: string;
  profileName: string;
  postText: string;
  postMedia: React.ReactNode;
  postStats: string;
};

export type SocialsSectionCard = {
  socialIcon: string;
  slides: SocialPost[];
};

export default function SocialsSection({
  socials,
  socialLinks
}: {
  socials?: any[];
  socialLinks?: {
    social_x?: string;
    social_tiktok?: string;
    social_instagram?: string;
    social_facebook?: string;
    social_youtube?: string;
    social_snapchat?: string;
  };
}) {
  // Use socials prop as the only data source
  const data = socials && socials.length > 0 ? socials : [];
  const [activeSlides, setActiveSlides] = useState(Array(data.length).fill(0));

  // Social platform configuration
  const getSocialConfig = (platform: string) => {
    const configs = {
      facebook: {
        icon: '/socials/Facebook.png',
        profileName: 'Al Ahly SC',
        profileUrl: socialLinks?.social_facebook,
        color: '#1877f3'
      },
      instagram: {
        icon: '/socials/instagram.png',
        profileName: 'alahly',
        profileUrl: socialLinks?.social_instagram,
        color: '#E1306C'
      }
    };
    return configs[platform as keyof typeof configs] || {
      icon: '/socials/Facebook.png',
      profileName: 'Al Ahly SC',
      profileUrl: '#',
      color: '#1877f3'
    };
  };

  // Compose cards from socials prop
  const cards = data.map((card, cardIdx) => {
    const socialConfig = getSocialConfig(card.platform);
    return {
      socialIcon: socialConfig.icon,
      slides: card.posts.map((post: any, slideIdx: number) => ({
        socialIcon: socialConfig.icon,
        profilePicture: (
          <span className="inline-block w-10 h-10 rounded-full overflow-hidden bg-gray-200 align-middle">
            <Image
              src="/logo.png"
              alt={socialConfig.profileName}
              width={40}
              height={40}
              className="w-full h-full object-cover object-center"
            />
          </span>
        ),
        profileName: (
          <a 
            href={socialConfig.profileUrl || '#'} 
            target="_blank" 
            rel="noopener noreferrer" 
            className="hover:underline font-bold"
            style={{ color: socialConfig.color }}
          >
            {socialConfig.profileName}
          </a>
        ),
      postText: post.message || post.caption || "",
      postMedia: post.full_picture || post.media_url ? (
        post.media_type === "VIDEO" ? (
          <a href={post.permalink} target="_blank" rel="noopener noreferrer">
            <video controls className="w-full h-[160px] object-cover rounded-lg">
              <source src={post.media_url} type="video/mp4" />
            </video>
          </a>
        ) : (
          <a href={post.permalink} target="_blank" rel="noopener noreferrer">
            <div className="w-full h-[160px] flex items-center justify-center bg-[#222] rounded-lg overflow-hidden">
              <ImageWithSpinner
                src={post.full_picture || post.media_url}
                alt="Post media"
                width={320}
                height={160}
                className="w-full h-full object-cover object-top rounded-lg"
                priority={cardIdx === 0 && slideIdx === 0}
                quality={70}
                loading={cardIdx === 0 && slideIdx === 0 ? 'eager' : 'lazy'}
                style={{ background: '#222' }}
              />
            </div>
          </a>
        )
      ) : (
        <div className="w-full h-[160px] bg-[#222] rounded-lg flex items-center justify-center text-gray-400 text-sm">
          لا يوجد صورة
        </div>
      ),
      postStats: post.posted_at ? new Date(post.posted_at).toLocaleDateString("ar-EG", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      }) : '',
    }))
    };
  });

  useEffect(() => {
    if (activeSlides.length !== cards.length) {
      setActiveSlides(Array(cards.length).fill(0));
    }
  }, [cards.length]);

  // Auto-slide logic
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  useEffect(() => {
    if (cards.length === 0) return;
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      setActiveSlides((prev) =>
        prev.map((val, idx) => {
          const slidesCount = cards[idx]?.slides.length || 1;
          return (val + 1) % slidesCount;
        })
      );
    }, 8000);
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [cards.length, cards.map(card => card.slides.length).join(",")]);

  // Detect direction (rtl/ltr) from document or locale
  const [dir, setDir] = useState<'rtl' | 'ltr'>('rtl');
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setDir(document.dir === 'ltr' ? 'ltr' : 'rtl');
    }
  }, []);

  // Pause scroll until image loads
  const [allImagesLoaded, setAllImagesLoaded] = useState(false);
  useEffect(() => {
    if (!cards.length) return;
    let totalImages = 0;
    let loadedImages = 0;
    cards.forEach(card => {
      card.slides.forEach((slide: any) => {
        if (slide.postMedia && slide.postMedia.type === ImageWithSpinner) {
          totalImages++;
          if (slide.postMedia.props && slide.postMedia.props.style && slide.postMedia.props.style.opacity === 1) {
            loadedImages++;
          }
        }
      });
    });
    if (totalImages > 0 && loadedImages === totalImages) {
      setAllImagesLoaded(true);
      document.body.style.overflow = '';
    } else if (totalImages > 0) {
      setAllImagesLoaded(false);
      document.body.style.overflow = 'hidden';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [cards]);

  return (
    <div className={`w-full min-h-fit lg:h-[800px] bg-[#333333] bg-[url(/img/socials-bg.png)] bg-cover flex flex-col justify-center items-center gap-6 lg:gap-10 mb-2 pt-12 pb-12`} dir={dir}>
      {/* Centered grid container */}
      <div className="w-full flex flex-col items-center">
        <div
          className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 lg:gap-10 xl:gap-12 w-full px-4 md:px-8 lg:px-16 xl:px-24"
          style={dir === 'ltr' ? { direction: 'ltr' } : {}}
        >
          {cards.map((card, index) => (
            <div key={index} className="flex flex-col items-center">
              <SocialCard
                {...card.slides[activeSlides[index]]}
              />
              <div className="flex justify-center mt-4 gap-2">
                {card.slides.map((_: any, dotIndex: number) => (
                  <div
                    key={dotIndex}
                    className={
                      [
                        'w-2 h-2 rounded-full cursor-pointer transition-all',
                        activeSlides[index] === dotIndex ? 'bg-white' : 'bg-gray-500',
                      ].join(' ')
                    }
                    onClick={() => {
                      const updated = [...activeSlides];
                      updated[index] = dotIndex;
                      setActiveSlides(updated);
                    }}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="w-full flex flex-row-reverse flex-wrap justify-center items-center gap-2 max-w-[400px] sm:max-w-none mt-8">
        {[
          { icon: "Facebook-icon.png", label: "Facebook", url: socialLinks?.social_facebook },
          { icon: "Instagram-icon.png", label: "Instagram", url: socialLinks?.social_instagram },
          { icon: "X-icon.png", label: "X", url: socialLinks?.social_x },
          { icon: "TikTok-icon.png", label: "TikTok", url: socialLinks?.social_tiktok },
          { icon: "Snapchat-icon.png", label: "Snapchat", url: socialLinks?.social_snapchat },
          { icon: "Youtube-icon.png", label: "YouTube", url: socialLinks?.social_youtube },
        ]
          .filter(social => social.url && social.url.trim() !== '') // Only show icons that have valid URLs
          .map((social, idx) => (
            <Link
              key={idx}
              href={social.url!}
              target="_blank"
              rel="noopener noreferrer"
              title={social.label}
              className="w-[42px] h-[42px] lg:w-[52px] lg:h-[52px] rounded-full bg-[#252323] flex justify-center items-center hover:bg-[#3a3838] transition-colors duration-200"
            >
              <Image
                src={`/socials/${social.icon}`}
                alt={`${social.label} Icon`}
                width={24}
                height={24}
                className="w-6 h-6 lg:w-[31px] lg:h-[31px] object-contain"
                onError={(e) => {
                  console.warn(`Failed to load social icon: /socials/${social.icon}`);
                  // Hide the image if it fails to load
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </Link>
          ))}
      </div>

      {/* Add spinner CSS */}
      <style jsx global>{`
        .loader {
          border-style: solid;
          border-width: 4px;
          border-radius: 50%;
          border-color: #e6c97b transparent #e6c97b transparent;
          animation: spin 1s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

// Custom ImageWithSpinner component
function ImageWithSpinner(props: React.ComponentProps<typeof Image>) {
  const [loading, setLoading] = useState(true);
  return (
    <div style={{ position: 'relative', width: props.width, height: props.height }}>
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-[#222] z-10">
          <span className="loader border-4 border-t-4 border-gray-200 border-t-[#e6c97b] rounded-full w-8 h-8 animate-spin"></span>
        </div>
      )}
      <Image
        {...props}
        onLoad={() => setLoading(false)}
        style={{ ...props.style, opacity: loading ? 0 : 1, transition: 'opacity 0.5s' }}
      />
    </div>
  );
}

