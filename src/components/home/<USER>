"use client";
import { useState, useEffect } from "react";
import translations from "@/lib/locales";
import { usePathname } from "next/navigation";
import Link from "next/link";

export type Video = {
  video_url: string;
  title: string;
  intro: string;
  image: string; // Added image property
};

export default function VideosSection({ videos }: { videos: Video[] }) {
  const pathname = usePathname();
  const locale = pathname ? pathname.split("/")[1] : "ar";
  const t = (key: string) => translations[locale]?.[key] || key;

  const [selected, setSelected] = useState(videos && videos.length > 0 ? videos[0] : undefined);

  // Always set first video as selected when videos changes
  useEffect(() => {
    if (Array.isArray(videos) && videos.length > 0) {
      setSelected(videos[0]);
    }
  }, [videos]);

  // Guard: if videos is not an array or is empty, show fallback UI
  if (!Array.isArray(videos) || videos.length === 0) {
    return (
      <div className="w-full md:h-[800px] bg-[url(/img/videos_section_bg.jpg)] bg-cover p-4 md:p-8 flex items-center justify-center">
        <div className="text-center text-gray-500 text-lg md:text-2xl font-semibold">
          {t("no_videos")}
        </div>
      </div>
    );
  }

  const renderVideos = () => {
    return videos.map((video, index) => (
      <div
        key={`${index}_${video.video_url}`}
        className={`cursor-pointer flex gap-2 md:gap-4 items-center p-2 hover:bg-gray-100/20 transition-colors duration-200 rounded-lg ${selected?.video_url === video.video_url ? 'bg-black/40' : 'bg-white/10'}`}
        onClick={() => setSelected(video)}
      >
        <div className="relative rounded-lg overflow-clip min-w-[100px] md:min-w-[160px] w-[100px] h-[56px] md:w-[160px] md:h-[90px] aspect-video flex items-center justify-center bg-black">
          <img
            src={video.image}
            alt={video.title}
            className="w-full h-full object-cover"
            width={160}
            height={90}
          />
          <span className="absolute inset-0 flex items-center justify-center">
            <svg width="40" height="40" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="24" cy="24" r="24" fill="rgba(0,0,0,0.5)" />
              <polygon points="20,16 34,24 20,32" fill="#fff" />
            </svg>
          </span>
        </div>
        <div className="flex flex-col">
          <p key="title" className={`text-sm md:text-lg font-bold line-clamp-2 ps-4 pe-4 ${selected?.video_url === video.video_url ? 'bg-black/60 text-white rounded px-2 py-1' : ''}`}>{video.title}</p>
          <p key="description" className={`text-xs md:text-sm font-normal line-clamp-2 ps-4 pe-4 ${selected?.video_url === video.video_url ? 'bg-black/40 text-white rounded px-2 py-1 mt-1' : ''}`}>{video.intro}</p>
        </div>
      </div>
    ));
  };

  return (
    <div className="relative w-full p-4 md:p-8 bg-[#000000] ">
      <div className="absolute inset-0 z-0 bg-[url(/img/videos_section_bg.jpg)] bg-cover bg-center" style={{ opacity: 0.15 }} />
      <div className="absolute inset-0 z-10 bg-white/10 pointer-events-none" />
      <div className="relative z-20">
        <div className="flex gap-3">
          <span className="h-5 w-5 bg-[#EC2028]"></span>
          <div className="flex flex-col gap-1 pt-3">
            <h5 className="text-2xl md:text-4xl font-bold">{t("videos")}</h5>
          </div>
        </div>
        <div className="w-full flex flex-col lg:flex-row justify-between gap-6 mt-6 md:mt-10">
          {/* Main Video */}
          <div className="w-full lg:w-2/3">
            <div className="rounded-lg overflow-clip">
              {selected ? (
                <iframe
                  className="w-full aspect-video"
                  src={selected.video_url}
                  title="YouTube video player"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  referrerPolicy="strict-origin-when-cross-origin"
                  allowFullScreen
                ></iframe>
              ) : (
                <div className="text-gray-400 py-20 text-center">{t("no_videos")}</div>
              )}
            </div>
            {selected && (
              <div className="p-3 md:p-5">
                <p
                  key="title"
                  className="text-base md:text-lg font-bold leading-7 md:leading-8 bg-black/60 text-white px-2 py-1 rounded inline-block"
                >
                  {selected.title}
                </p>
                <p key="description" className="text-xs md:text-sm text-gray-200 font-normal bg-black/40 px-2 py-1 rounded inline-block mt-2">
                  {selected.intro}
                </p>
              </div>
            )}
          </div>
          {/* Video List */}
          <div className="w-full lg:w-1/3 flex flex-col gap-4">
            <div className="flex flex-col gap-2 overflow-auto max-h-[400px] md:max-h-[460px] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-white [&::-webkit-scrollbar-thumb]:bg-[#EC2028] pe-3 md:pe-4">
              {renderVideos()}
            </div>
            <Link
              href={`/${locale}/media/videos`}
              className="px-4 py-2 md:px-6 md:py-3 bg-[#EC2028] rounded-xs cursor-pointer font-semibold text-sm md:text-base text-white hover:bg-[#b71c1c] transition text-center self-center lg:self-end"
              tabIndex={videos.length === 0 ? -1 : 0}
              aria-disabled={!videos.length}
            >
              {t("show_more")}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
