"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { useGlobalData } from "@/context/GlobalDataContext";

interface HomePageWrapperProps {
  children: React.ReactNode;
  locale: string;
}

export default function HomePageWrapper({ children, locale }: HomePageWrapperProps) {
  const [showBanner, setShowBanner] = useState(false);
  const { top_banner } = useGlobalData();
  const pathname = usePathname();

  useEffect(() => {
    // Check if top banner is shown (sessionStorage logic must match Navbar)
    const isHome = pathname === `/${locale}` || pathname === `/${locale}/`;
    const bannerClosed = typeof window !== 'undefined' && sessionStorage.getItem('top_banner_closed') === '1';
    setShowBanner(!!top_banner && isHome && !bannerClosed);
  }, [pathname, locale, top_banner]);

  // Listen for banner close event from Navbar and update padding immediately
  useEffect(() => {
    function checkBanner() {
      const isHome = pathname === `/${locale}` || pathname === `/${locale}/`;
      const bannerClosed = typeof window !== 'undefined' && sessionStorage.getItem('top_banner_closed') === '1';
      setShowBanner(!!top_banner && isHome && !bannerClosed);
    }
    
    function handleStorage(e: StorageEvent) {
      if (e.key === 'top_banner_closed') {
        checkBanner();
      }
    }
    
    function handleBannerClosed() {
      checkBanner();
    }

    window.addEventListener('storage', handleStorage);
    window.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') checkBanner();
    });
    window.addEventListener('top_banner_closed', handleBannerClosed);
    
    checkBanner();
    
    return () => {
      window.removeEventListener('storage', handleStorage);
      window.removeEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') checkBanner();
      });
      window.removeEventListener('top_banner_closed', handleBannerClosed);
    };
  }, [pathname, locale, top_banner]);

  return (
    <main className="flex flex-col gap-3 min-h-fit text-white">
      <div className={`flex flex-col gap-3 row-start-2 items-center sm:items-start min-h-fit px-2 text-white transition-all duration-300 ${showBanner ? 'pt-32 lg:pt-106' : 'pt-12 lg:pt-40'}`}>
        {children}
      </div>
    </main>
  );
}
