"use client";
import Image from "next/image";
import { useState, useRef, useEffect } from "react";
import translations from "@/lib/locales";

interface VideoHighlightSectionProps {
    video: any;
    locale: string;
}

export default function VideoHighlightSection({ video, locale }: VideoHighlightSectionProps) {
    const [showModal, setShowModal] = useState(false);
    const modalRef = useRef<HTMLDivElement>(null);
    const t = (key: string) => translations[locale]?.[key] || key;
    let ytId = "";
    if (video?.video_url) {
        ytId = video.video_url || "";
    }

    useEffect(() => {
        if (showModal && modalRef.current) {
            modalRef.current.focus();
        }
    }, [showModal]);

    return (
        <section className="w-full p-4 sm:p-6 md:p-8 text-white">
            <div className="relative w-full h-[500px] md:h-[600px] rounded-lg overflow-hidden flex items-center justify-center bg-black">
                <Image src={video?.image} alt={video?.title} fill className="object-cover opacity-70" />
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                    <h3 className="text-2xl font-bold text-white mb-2">{video?.title}</h3>
                    <p className="text-white mb-4">{video?.intro}</p>
                    {ytId && (
                        <button
                            className="bg-[#EC2028] text-white px-6 py-2 rounded font-bold flex items-center gap-2 text-lg shadow-lg hover:bg-[#b81a22] transition"
                            onClick={() => setShowModal(true)}
                            type="button"
                        >
                            <span>▶</span> {t("watch_now") || "شاهد الآن"}
                        </button>
                    )}
                </div>
                {showModal && ytId && (
                    <div
                        className="fixed inset-0 z-[1000002] flex items-center justify-center bg-black bg-opacity-80 top-0 left-0 w-screen h-screen"
                        onClick={() => setShowModal(false)}
                        tabIndex={0}
                        ref={modalRef}
                        onKeyDown={e => { if (e.key === 'Escape') setShowModal(false); }}
                    >
                        <div
                            className="relative w-[90vw] max-w-3xl h-[60vw] max-h-[70vh] bg-black rounded-lg flex flex-col items-center justify-center"
                            onClick={e => e.stopPropagation()}
                        >
                            <button
                                className="absolute top-2 right-2 text-white text-2xl bg-black bg-opacity-60 rounded-full px-3 py-1 hover:bg-opacity-90"
                                onClick={() => setShowModal(false)}
                                aria-label="Close"
                                type="button"
                            >
                                ×
                            </button>
                            <iframe
                                width="100%"
                                height="100%"
                                src={`${ytId}?autoplay=1`}
                                title="YouTube video player"
                                frameBorder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                allowFullScreen
                                className="rounded-lg w-full h-full"
                            ></iframe>
                        </div>
                    </div>
                )}
            </div>
        </section>
    );
}
