"use client";
import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

export default function RouteLoading() {
  const [loading, setLoading] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    // Only show loading spinner if navigating to a different route (not on initial mount)
    if (typeof window !== 'undefined' && window.performance && window.performance.navigation.type === 1) {
      // If it's a reload, don't show spinner
      setLoading(false);
      return;
    }
    setLoading(true);
    const timeout = setTimeout(() => setLoading(false), 600); // show at least 600ms
    return () => clearTimeout(timeout);
  }, [pathname]);

  if (!loading) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <div className="w-16 h-16 border-4 border-[#EC2028] border-t-transparent rounded-full animate-spin"></div>
    </div>
  );
}
