"use client";
import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Modal from "./Modal";
import { searchNews } from "../lib/api";

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  locale: string;
  translations: any;
}

interface NewsResult {
  id: number;
  slug: string;
  title: string;
  intro: string;
  image: string;
  published_at: string;
  category: {
    title: string;
  };
}

export default function SearchModal({ isOpen, onClose, locale, translations }: SearchModalProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<NewsResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const router = useRouter();

  // Focus input when modal opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  // Handle search submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      onClose();
      router.push(`/${locale}/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  // Handle live search with debouncing
  const handleSearchChange = async (value: string) => {
    setSearchQuery(value);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (value.trim().length > 2) {
      // Set a new timeout for debounced search
      searchTimeoutRef.current = setTimeout(async () => {
        setIsSearching(true);
        try {
          const response = await searchNews(value.trim(), locale, 1, 3);
          if (response.status && response.news) {
            setSearchResults(response.news);
          } else {
            setSearchResults([]);
          }
        } catch (error) {
          console.error('Live search error:', error);
          setSearchResults([]);
        } finally {
          setIsSearching(false);
        }
      }, 300);
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  if (!isOpen) return null;

  return (
    <Modal onClose={onClose}>
      <div className="search-modal-content bg-white rounded-xl shadow-2xl w-full max-w-2xl mx-4 overflow-hidden ring-1 ring-black ring-opacity-5">
        {/* Header */}
        <div className="bg-gradient-to-r from-[#C60428] to-[#a00320] text-white p-4">
          <h2 className="text-xl font-bold text-center">
            {translations[locale]?.['search'] || 'Search'}
          </h2>
        </div>

        {/* Search Form */}
        <div className="p-6">
          <form onSubmit={handleSearchSubmit} className="mb-4">
            <div className="relative">
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                placeholder={translations[locale]?.['search_placeholder'] || 'Search news, sports, and more...'}
                className="search-input w-full px-4 py-3 ps-12 border-2 border-gray-300 rounded-lg focus:border-[#C60428] focus:outline-none text-lg text-gray-900 placeholder:text-gray-400 placeholder:font-normal"
              />
              <button
                type="submit"
                className="absolute end-2 top-1/2 transform -translate-y-1/2 bg-[#C60428] text-white p-2 rounded-lg hover:bg-[#a00320] transition-colors"
              >
                <Image
                  src="/search_icon.svg"
                  alt="Search"
                  width={20}
                  height={20}
                  className="w-5 h-5"
                />
              </button>
            </div>
          </form>

          {/* Search Results Preview */}
          {isSearching && (
            <div className="text-center py-6">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-2 search-spinner"></div>
              <p className="mt-3 text-gray-600 font-medium">
                {translations[locale]?.['searching'] || 'Searching...'}
              </p>
            </div>
          )}

          {!isSearching && searchQuery.trim().length > 2 && searchResults.length === 0 && (
            <div className="text-center py-8">
              <div className="mb-4">
                <Image
                  src="/search_icon.svg"
                  alt="No results"
                  width={48}
                  height={48}
                  className="w-12 h-12 mx-auto opacity-30"
                />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {translations[locale]?.['no_results_found'] || 'No results found'}
              </h3>
              <p className="text-gray-600 mb-4">
                {translations[locale]?.['no_results_message'] || 'Try different keywords or check spelling'}
              </p>
              <button
                onClick={() => {
                  onClose();
                  router.push(`/${locale}/search?q=${encodeURIComponent(searchQuery.trim())}`);
                }}
                className="text-[#C60428] hover:text-[#a00320] font-medium hover:underline transition-colors"
              >
                {translations[locale]?.['search_all_content'] || 'Search all content'}
              </button>
            </div>

          )}

          <div className="flex justify-end mt-4">
            <button
              onClick={() => {
                onClose();
                router.push(`/${locale}/search`);
              }}
              className="text-[#C60428] font-medium hover:underline text-sm"
            >
              {translations[locale]?.['advanced_search'] || 'Advanced Search'}
            </button>
          </div>
          {searchResults.length > 0 && !isSearching && (
            <div className="mt-4">
              <h3 className="font-semibold mb-3 text-gray-900 text-sm uppercase tracking-wide">
                {translations[locale]?.['quick_results'] || 'Quick Results'}
              </h3>
              <div className="space-y-3">
                {searchResults.slice(0, 3).map((result) => (
                  <div
                    key={result.id}
                    className="search-result-item p-3 hover:bg-gray-50 rounded-lg cursor-pointer border border-transparent hover:border-gray-200 transition-all duration-200"
                    onClick={() => {
                      onClose();
                      router.push(`/${locale}/news/article/${result.slug}`);
                    }}
                  >
                    <div className="flex gap-3">
                      {/* Image Thumbnail */}
                      <div className="flex-shrink-0">
                        <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100">
                          {result.image ? (
                            <Image
                              src={result.image}
                              alt={result.title}
                              width={64}
                              height={64}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                              <Image
                                src="/news_placeholder.svg"
                                alt="No image"
                                width={24}
                                height={24}
                                className="w-6 h-6 opacity-40"
                              />
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <p className="search-title font-semibold text-gray-900 line-clamp-2 mb-1 hover:text-[#C60428] transition-colors" style={{ opacity: 1, color: '#111827' }}>
                          {result.title}
                        </p>
                        <p className="search-intro text-sm text-gray-600 line-clamp-2 mb-2" style={{ opacity: 1, color: '#4B5563' }}>
                          {result.intro}
                        </p>
                        <div className="flex items-center justify-between">
                          <p className="text-xs text-white font-medium bg-[#C60428] px-2 py-1 rounded">
                            {result.category?.title}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(result.published_at).toLocaleDateString(locale)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {searchResults.length > 3 && (
                  <div className="text-center pt-3 border-t border-gray-100">
                    <button
                      onClick={() => {
                        onClose();
                        router.push(`/${locale}/search?q=${encodeURIComponent(searchQuery.trim())}`);
                      }}
                      className="text-[#C60428] font-medium hover:text-[#a00320] hover:underline transition-colors inline-flex items-center gap-1"
                    >
                      {translations[locale]?.['view_all_results'] || 'View all results'}
                      <span className="text-xs">({searchResults.length})</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="mt-6 pt-4 border-t border-gray-200 rounded-b-xl shadow-inner">
            <p className="text-sm mb-3 font-medium" style={{ color: '#C60428' }}>
              {translations[locale]?.['quick_links'] || 'Quick Links'}
            </p>
            <div className="flex flex-wrap gap-2">
              {[
                { label: translations[locale]?.['news'] || 'News', url: `/${locale}/news` },
                { label: translations[locale]?.['club'] || 'Club', url: `/${locale}/club` },
                { label: translations[locale]?.['academy'] || 'Academy', url: `/${locale}/academy/about` },
                { label: translations[locale]?.['alahly_magazine'] || 'Al Ahly Magazine', url: `/${locale}/magazine/list` },
              ].map((link, index) => (
                <button
                  key={index}
                  onClick={() => {
                    onClose();
                    router.push(link.url);
                  }}
                  className="quick-link-btn px-4 py-2 bg-[#DFB254] hover:bg-[[#C60428]] text-white hover:text-[#C60428] rounded-full text-sm font-medium transition-all duration-200 transform hover:scale-105"
                  style={{ transition: 'color 0.2s, background 0.2s' }}
                >
                  {link.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
}
