"use client";
import Image from "next/image";
import { useState } from "react";

export default function NewsImageWithFallback({ src, alt, ...props }: { src: string; alt: string; [key: string]: any }) {
  const [imgSrc, setImgSrc] = useState(src || "/img/default_placeholder.png");
  return (
    <Image
      {...props}
      src={imgSrc}
      alt={alt}
      onError={() => setImgSrc("/img/default_placeholder.png")}
    />
  );
}
