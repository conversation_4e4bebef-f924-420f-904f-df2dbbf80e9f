'use client';
import { useEffect, useState } from 'react';
import NewsCard from './news-card';
import { fetchApi } from '@/lib/api';
import translations from '@/lib/locales';

interface NewsItem {
    id: number;
    title: string;
    slug: string;
    intro: string;
    image: string;
    image_full: string; // أضفنا image_full هنا
    video_url: string; // Added to match NewsCard's NewsItem
    published_at: string;
    category?: {
        title: string;
    };
}

interface Category {
    id: number;
    slug: string;
    title: string;
}

interface NewsGridProps {
    locale: string;
    title?: string;
    categories?: Category[];
    displayCategories?: boolean;
    initialType?: string; // Added for API query param
    initialNews?: NewsItem[]; // Added for initial news
    initialPagination?: any; // Added for initial pagination
}

export default function NewsPage({ locale, title = 'آخر الأخـبار', categories: initialCategories, displayCategories = true, initialType = '1', initialNews, initialPagination }: NewsGridProps) {
    const [categories, setCategories] = useState<Category[]>(initialCategories || []);
    const [selectedCategory, setSelectedCategory] = useState<string>('all');
    const [news, setNews] = useState<NewsItem[]>(
        (initialNews || []).map((item: any) => ({
            ...item,
            image_full: item.image_full
        }))
    ); // Use initialNews if provided
    const [page, setPage] = useState<number>(1);
    const [lastPage, setLastPage] = useState<number>(initialPagination?.last_page || 1);
    const [hasInteracted, setHasInteracted] = useState(false);
    const [loading, setLoading] = useState(false);

    const fetchCategories = async () => {
        if (displayCategories) {
            if (initialCategories && initialCategories.length > 0) {
                setCategories([{ id: 0, slug: 'all', title: translations[locale]?.all || 'الكل' }, ...initialCategories]);
                setSelectedCategory('all');
                return;
            }
            const res = await fetchApi(`/api/news/categories`, {
                headers: { "X-localization": locale },
            });
            setCategories([{ id: 0, slug: 'all', title: translations[locale]?.all || 'الكل' }, ...res.categories]);
            setSelectedCategory('all');
        }
    };

    const fetchNews = async () => {
        setLoading(true);
        let url = `/api/news/category/${selectedCategory}?page=${page}`;
        if (initialType) {
            url += `&type=${encodeURIComponent(initialType)}`;
        }
        try {
            const res = await fetchApi(url, {
                headers: { "X-localization": locale },
            });
            setNews(
                (res.news || []).map((item: any) => ({
                    ...item,
                    video_url: typeof item.video_url === 'string' ? item.video_url : '',
                    image_full: item.image_full
                }))
            );
            setLastPage(res.pagination?.last_page || 1);
        } catch (e) {
            setNews([]);
            setLastPage(1);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchCategories();
    }, []);

    useEffect(() => {
        // Only skip API call for initial render
        if (initialNews && page === 1 && selectedCategory === 'all' && !hasInteracted) return;
        fetchNews();
    }, [selectedCategory, page]);

    useEffect(() => {
        // Mark as interacted if user changes category or page (but not on initial render)
        if (!(page === 1 && selectedCategory === 'all')) {
            setHasInteracted(true);
        }
    }, [selectedCategory, page]);

    return (
        <section className="w-full mx-auto px-4">
            {/* Title and Top Pagination */}
            <div className="flex items-center justify-between mb-4 flex-wrap gap-y-2">
                <h2 className="text-2xl font-bold text-[#272525]">{title}</h2>
                {/* Desktop Pagination */}
                {lastPage > 1 && (
                    <div className="hidden sm:flex gap-1 w-auto min-w-0 max-w-full flex-shrink flex-wrap">
                        <button 
                            type="button"
                            disabled={page <= 1} 
                            onClick={() => setPage(page - 1)} 
                            className={`px-3 py-1 border rounded ${page <= 1 ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                        >
                            &lt;
                        </button>
                        {[...Array(lastPage)].map((_, i) => {
                            const pageNum = i + 1;
                            if (
                                pageNum === 1 || 
                                pageNum === lastPage || 
                                pageNum === page || 
                                pageNum === page - 1 || 
                                pageNum === page + 1
                            ) {
                                return (
                                    <button
                                        key={i}
                                        onClick={() => setPage(pageNum)}
                                        className={`px-3 py-1 rounded ${page === pageNum ? 'bg-[#EC2028] text-white' : 'border'} ${page === pageNum ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                                        disabled={page === pageNum}
                                    >
                                        {pageNum}
                                    </button>
                                );
                            }
                            if ((pageNum === 2 && page > 3) || (pageNum === lastPage - 1 && page < lastPage - 2)) {
                                return <span key={i} className="px-2 py-1">...</span>;
                            }
                            return null;
                        })}
                        <button 
                            disabled={page >= lastPage} 
                            onClick={() => setPage(page + 1)} 
                            className={`px-3 py-1 border rounded ${page >= lastPage ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                        >
                            &gt;
                        </button>
                    </div>
                )}
            </div>
            {/* Mobile Pagination */}
            {lastPage > 1 && (
                <div className="flex sm:hidden gap-1 mb-4 justify-center w-auto min-w-0 max-w-full flex-shrink flex-wrap">
                    <button 
                        disabled={page <= 1} 
                        onClick={() => setPage(page - 1)} 
                        className={`px-3 py-1 border rounded ${page <= 1 ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                    >
                        &lt;
                    </button>
                    {[...Array(lastPage)].map((_, i) => {
                        const pageNum = i + 1;
                        if (
                            pageNum === 1 || 
                            pageNum === lastPage || 
                            pageNum === page || 
                            pageNum === page - 1 || 
                            pageNum === page + 1
                        ) {
                            return (
                                <button
                                    key={i}
                                    onClick={() => setPage(pageNum)}
                                    className={`px-3 py-1 rounded ${page === pageNum ? 'bg-[#EC2028] text-white' : 'border'} ${page === pageNum ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                                    disabled={page === pageNum}
                                >
                                    {pageNum}
                                </button>
                            );
                        }
                        if ((pageNum === 2 && page > 3) || (pageNum === lastPage - 1 && page < lastPage - 2)) {
                            return <span key={i} className="px-2 py-1">...</span>;
                        }
                        return null;
                    })}
                    <button 
                        disabled={page >= lastPage} 
                        onClick={() => setPage(page + 1)} 
                        className={`px-3 py-1 border rounded ${page >= lastPage ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                    >
                        &gt;
                    </button>
                </div>
            )}
            {/* الأقسام */}
            {displayCategories && (
                <div className="flex gap-2 overflow-x-auto border-b pb-2 mb-4">
                    {categories.map((cat) => (
                        <button
                            key={cat.slug}
                            className={`px-4 py-2 rounded-t font-bold text-sm whitespace-nowrap ${selectedCategory === cat.slug ? 'bg-[#EC2028] text-white' : 'bg-white text-[#EC2028] border border-[#EC2028]'} hover:bg-[#EC2028] hover:text-white cursor-pointer`}
                            onClick={() => {
                                setSelectedCategory(cat.slug);
                                setPage(1);
                            }}
                        >
                            {cat.title}
                        </button>
                    ))}
                </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {loading ? (
                    Array.from({ length: 9 }).map((_, i) => (
                        <div key={i} className="col-span-1 h-64 w-full overflow-hidden rounded-lg bg-gray-200 relative">
                            <div className="h-40 w-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer" style={{backgroundSize: '200% 100%'}} />
                            <div className="p-4">
                                <div className="h-4 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer rounded w-3/4 mb-2" style={{backgroundSize: '200% 100%'}} />
                                <div className="h-3 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer rounded w-1/2 mb-1" style={{backgroundSize: '200% 100%'}} />
                                <div className="h-3 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer rounded w-1/3" style={{backgroundSize: '200% 100%'}} />
                            </div>
                        </div>
                    ))
                ) : Array.isArray(news) && news.length === 0 ? (
                    <div className="col-span-3 text-center text-gray-500 py-12 text-lg font-cairo">
                        {initialType === '3'
                            ? (translations[locale]?.no_pictures || 'لا توجد صور')
                            : initialType === '2'
                            ? (translations[locale]?.no_videos || 'لا توجد فيديوهات')
                            : (translations[locale]?.no_news || 'لا توجد أخبار')}
                    </div>
                ) : (
                    (Array.isArray(news) ? news : []).map((item) => (
                        <NewsCard key={item.id} item={item} locale={locale} type={initialType} />
                    ))
                )}
            </div>
        </section>
    );
}
