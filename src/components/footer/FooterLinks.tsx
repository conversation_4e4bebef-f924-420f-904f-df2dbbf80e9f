import React from "react";
import Link from "next/link";
import translations from "@/lib/locales";
import { usePathname } from "next/navigation";
import { useGlobalData } from "@/context/GlobalDataContext";

export default function FooterLinks() {
  const { sports } = useGlobalData();
  const locale = (() => {
    const pathname = usePathname();
    if (!pathname) return "ar";
    const segments = pathname.split("/").filter(Boolean);
    return segments.length > 0 ? segments[0] : "ar";
  })();
  const t = (key: string) => translations[locale]?.[key] || key;
  const footerData = require("@/data/footer.json");

  return (
    <div className="flex w-full justify-center">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-stretch w-full max-w-screen-xl">
        {footerData.sections.map((section: any, index: number) => {
          if (section.title === "sports" && sports && sports.length > 0) {
            return (
              <React.Fragment key={section.title}>
                <div className="mb-2 md:mb-0 flex flex-col h-full justify-between">
                  <h4 className="text-base md:text-lg font-bold mb-2 md:mb-3 truncate">
                    {t(section.title)}
                  </h4>
                  <div className="flex flex-col gap-2 flex-1">
                    {sports.map((sport: any) => (
                      <Link
                        key={sport.id}
                        href={`/${locale}/sports/${sport.slug}`}
                        className="text-xs md:text-sm hover:underline truncate block font-normal"
                        title={sport.title}
                      >
                        {sport.title}
                      </Link>
                    ))}
                  </div>
                </div>
              </React.Fragment>
            );
          }
          if (section.title === "alahly_club") {
            return (
              <React.Fragment key={section.title}>
                <div className="mb-2 md:mb-0 flex flex-col h-full justify-between col-span-2 md:col-span-2">
                  <h4 className="text-base md:text-lg font-bold mb-2 md:mb-3 truncate">
                    {t(section.title)}
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-2 gap-2 flex-1">
                    {section.links.map((link: any, index: number) => {
                      const isExternal = link.url && (link.url.startsWith('http://') || link.url.startsWith('https://'));
                      const href = isExternal ? link.url : `/${locale}${link.url}`;
                      if (isExternal) {
                        return (
                          <a
                            key={`${index}_${link.label}_${link.url}`}
                            href={href}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs md:text-sm hover:underline truncate block font-normal"
                            title={t(link.label.trim())}
                          >
                            {t(link.label.trim())}
                          </a>
                        );
                      }
                      return (
                        <Link
                          key={`${index}_${link.label}_${link.url}`}
                          href={href}
                          className="text-xs md:text-sm hover:underline truncate block font-normal"
                          title={t(link.label.trim())}
                        >
                          {t(link.label.trim())}
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </React.Fragment>
            );
          }
          return (
            <React.Fragment key={section.title}>
              <div className="mb-2 md:mb-0 flex flex-col h-full justify-between">
                <h4 className="text-base md:text-lg font-bold mb-2 md:mb-3 truncate">
                  {t(section.title)}
                </h4>
                <div className="flex flex-col gap-2 font-semibold flex-1">
                  {section.links.map((link: any, index: number) => {
                    const isExternal = link.url && (link.url.startsWith('http://') || link.url.startsWith('https://'));
                    const href = isExternal ? link.url : `/${locale}${link.url}`;
                    if (link.label === 'download_pdf' || isExternal) {
                      return (
                        <a
                          key={`${index}_${link.label}_${link.url}`}
                          href={href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs md:text-sm hover:underline truncate block font-normal"
                          title={t(link.label.trim())}
                        >
                          {t(link.label.trim())}
                        </a>
                      );
                    }
                    return (
                      <Link
                        key={`${index}_${link.label}_${link.url}`}
                        href={href}
                        className="text-xs md:text-sm hover:underline truncate block font-normal"
                        title={t(link.label.trim())}
                      >
                        {t(link.label.trim())}
                      </Link>
                    );
                  })}
                </div>
              </div>
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
}
