import React from "react";
import Link from "next/link";

export default function FooterCopyright({ locale, t }: { locale: string, t: (key: string) => string }) {
  return (
    <div className="w-full flex flex-col md:flex-row items-center md:items-center justify-center md:justify-between px-4 md:px-6 py-3 md:py-4 font-normal bg-[#000000] text-sm md:text-base relative">
      <div className="flex flex-col md:flex-row items-center md:items-center w-full md:w-auto gap-2 md:gap-0 md:justify-center md:text-center">
        <div className="flex flex-row flex-wrap items-center justify-center md:justify-center gap-2 md:gap-3 z-10 w-full md:w-auto">
          <span className="text-center md:text-center whitespace-nowrap">
            {t("footer_rights")} <span className="text-[#D7AB4D] font-bold">{t("alahly_club")}</span> © {new Date().getFullYear()}
          </span>
          <div className="flex gap-2 md:gap-3 whitespace-nowrap">
            <Link href={`/${locale}/terms-and-conditions`} className="hover:underline font-normal" title={t("terms") ? t("terms") : "Terms & Conditions"}>
              {t("terms") ? t("terms") : "Terms & Conditions"}
            </Link>
            |
            <Link href={`/${locale}/privacy-policy`} className="hover:underline font-normal" title={t("privacy") ? t("privacy") : "Privacy Policy"}>
              {t("privacy") ? t("privacy") : "Privacy Policy"}
            </Link>
          </div>
        </div>
      </div>
      <span className="text-center md:text-end w-full md:w-auto mt-2 md:mt-0 md:ml-0 md:mr-0 md:justify-end font-normal">
        {t("footer_made_by")} <Link href="https://icons.technology" target="_blank" title="Icons Technology" rel="nofollow noopener noreferrer" className="text-[#D7AB4D] font-bold">ICONS</Link>
      </span>
    </div>
  );
}
