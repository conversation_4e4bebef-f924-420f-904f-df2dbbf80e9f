import React from "react";
import Image from "next/image";

export default function FooterSponsors({ sponsors }: { sponsors: any[] }) {
  return (
    <div className="flex flex-wrap items-center justify-center gap-8 bg-white rounded-xl py-12 shadow">
      {sponsors.map((sponsor: any, index: number) => {
        const isFocus = sponsor.focus;
        const width = isFocus ? 280 : 140;
        const height = isFocus ? 300 : 150;
        const imgClass = isFocus ? "h-20 w-auto object-contain" : "h-10 w-auto object-contain";
        const sponsorImage = (
          <Image
            src={`${sponsor.logo}`}
            alt={sponsor.name}
            width={width}
            height={height}
            className={imgClass}
          />
        );
        if (sponsor.website && sponsor.website.trim() !== "") {
          return (
            <a
              key={`${index}_${sponsor.name}`}
              href={sponsor.website}
              target="_blank"
              rel="noopener noreferrer"
              title={sponsor.name}
              style={{ display: 'inline-block' }}
            >
              {sponsorImage}
            </a>
          );
        }
        return (
          <div key={`${index}_${sponsor.name}`} className="flex-shrink-0">
            {sponsorImage}
          </div>
        );
      })}
    </div>
  );
}
