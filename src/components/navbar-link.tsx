import Link from "next/link";
import Image from "next/image";

interface NavLinkProps {
    link: string
    label?: string
    icon?: string
}

export default function NavLink({ link, label, icon}: NavLinkProps) {
  return (
    <Link href={link}>
      <div className="flex justify-between items-center gap-2 font-bold">
        {label} {icon ? <Image src={icon} width={24} height={24} alt={label ?? link}/> : null}
      </div>
    </Link>
  )
}