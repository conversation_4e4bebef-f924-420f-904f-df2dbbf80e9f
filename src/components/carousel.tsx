'use client';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import 'flickity/css/flickity.css';

const players = [
  { id: 1, name: 'محمد الشناوي', role: 'حارس مرمى / كابتن الفريق', image: '/players/1.png' },
  { id: 2, name: 'اللاعب 2', role: 'مدافع', image: '/players/2.png' },
  { id: 3, name: 'اللاعب 3', role: 'حارس مرمى / كابتن الفريق', image: '/players/3.png' },
  { id: 4, name: 'اللاعب 4', role: 'وسط ميدان', image: '/players/4.png' },
  { id: 5, name: 'اللاعب 5', role: 'مهاجم', image: '/players/5.png' },
];

export default function PlayerCarousel() {
  const [activeIndex, setActiveIndex] = useState(
      Math.floor(players.length / 2)
    );
    const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  
    const next = () => {
      setActiveIndex((prev) => (prev + 1) % players.length);
      setIsAutoPlaying(false);
    };
  
    const prev = () => {
      setActiveIndex((prev) => (prev - 1 + players.length) % players.length);
      setIsAutoPlaying(false);
    };
  
    const getStyleForPlayer = (index: number) => {
      let diff = index - activeIndex;
      const half = Math.floor(players.length / 2);
  
      if (diff > half) diff -= players.length;
      if (diff < -half) diff += players.length;
  
      const distance = Math.abs(diff);
  
      let scale = 1;
      let opacity = 1;
      let zIndex = 30;
      let translateX = diff * 240;
  
      if (index === activeIndex) {
        scale = 1.15;
        zIndex = 50;
        opacity = 1;
        translateX = 0;
      } else if (distance <= 3) {
        scale = 1 - distance * 0.07;
        opacity = 1 - distance * 0.25;
        zIndex = 30 - distance * 5;
      } else {
        scale = 0.8;
        opacity = 0;
        zIndex = 0;
      }
  
      return {
        position: "absolute",
        left: "45%",
        top: 0,
        transform: `translateX(${translateX}px) scale(${scale})`,
        opacity,
        zIndex,
        transition: "transform 0.5s ease, opacity 0.5s ease",
        transformOrigin: "center center",
        width: "200px",
        display: opacity === 0 ? "none" : "flex",
        flexDirection: opacity === 0 ? undefined : "column",
        alignItems: opacity === 0 ? undefined : "center",
        justifyContent: opacity === 0 ? undefined : "flex-end",
      } as React.CSSProperties;
    };
  
    useEffect(() => {
      if (!isAutoPlaying) return;
      const interval = setInterval(() => {
        setActiveIndex((prev) => (prev + 1) % players.length);
      }, 3000);
  
      return () => clearInterval(interval);
    }, [isAutoPlaying]);

  return (
    <div className="bg-[#1c1c1c] py-8 text-center text-white">
      <div className="flex gap-3">
              <span className="h-5 w-5 bg-[#DEB65D]"></span>
              <div className="flex flex-col gap-1 pt-3">
                <h5 className="text-2xl md:text-3xl text-gray-300">
                  كـــرة القـــدم
                </h5>
                <h5 className="text-3xl md:text-4xl font-bold">الفريق الأول</h5>
              </div>
            </div>
            <div className="relative w-full h-[450px] mx-auto overflow-hidden mt-8 md:mt-10">
              <div className="relative w-full h-full">
                {players.map((player, index) => {
                  const styles = getStyleForPlayer(index);
                  return (
                    <div key={`${player.id}-${index}`} style={styles}>
                      <Image
                        src={player.image}
                        alt={player.name}
                        width={200}
                        height={280}
                        className="object-cover rounded-md h-[240px] w-[180px] md:h-[280px] md:w-[200px]"
                      />
                      <div
                        className={`mt-3 text-center transition-opacity duration-300 ${
                          index === activeIndex ? "opacity-100" : "opacity-0"
                        }`}
                      >
                        <p className="text-sm md:text-lg font-bold">{player.name}</p>
                        <p className="text-xs md:text-sm text-[#DEB65D]">
                          {player.role}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
      
              <button
                onClick={prev}
                className="absolute right-0 top-1/2 -translate-y-1/2 z-40 text-white border border-[#DEB65D] px-3 py-1 md:px-4 md:py-2 rounded-lg hover:bg-[#DEB65D] hover:text-black transition-colors duration-300"
              >
                ❮
              </button>
      
              <button
                onClick={next}
                className="absolute left-0 top-1/2 -translate-y-1/2 z-40 text-white border border-[#DEB65D] px-3 py-1 md:px-4 md:py-2 rounded-lg hover:bg-[#DEB65D] hover:text-black transition-colors duration-300"
              >
                ❯
              </button>
            </div>
      
            {/* Dots indicator */}
            <div className="flex justify-center gap-2 mt-6">
              {players.map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setActiveIndex(index);
                    setIsAutoPlaying(false);
                  }}
                  className={`w-3 h-3 rounded-full cursor-pointer ${
                    index === activeIndex ? "bg-[#DEB65D]" : "bg-gray-600"
                  }`}
                />
              ))}
            </div>
    </div>
  );
}