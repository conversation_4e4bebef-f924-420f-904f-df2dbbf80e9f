import React from "react";

export default function Modal({ children, onClose }: { children: React.ReactNode; onClose: () => void }) {
  React.useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    document.addEventListener("keydown", handleEsc);
    return () => document.removeEventListener("keydown", handleEsc);
  }, [onClose]);

  return (
    <div className="fixed inset-0 z-[1000002] flex items-center justify-center bg-black/60" onClick={onClose}>
      <div
        className="relative bg-transparent rounded-xl shadow-xl flex flex-col"
        style={{ minWidth: 320 }}
        onClick={e => e.stopPropagation()}
      >
        <button
          onClick={onClose}
          className="absolute top-2 end-2 text-white bg-[#C60428] rounded-full w-8 h-8 flex items-center justify-center z-20 hover:bg-[#a00320] transition"
          aria-label="Close"
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none"><path d="M6 6l8 8M6 14L14 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/></svg>
        </button>
        {children}
      </div>
    </div>
  );
}
