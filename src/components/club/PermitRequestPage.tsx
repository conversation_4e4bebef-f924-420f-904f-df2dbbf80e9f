"use client";
import { fetchApi } from "@/lib/api";
import translations from "@/lib/locales";
import { useState } from "react";
import Image from "next/image";

interface PermitRequestPageProps {
    locale: string;
}

export default function PermitRequestPage({ locale }: PermitRequestPageProps) {
    const t = (key: string) => translations[locale]?.[key] || key;
    const [form, setForm] = useState({
        name: "",
        email: "",
        phone: "",
        organization: "",
        message: "",
    });
    const [files, setFiles] = useState<File[]>([]);
    const [loading, setLoading] = useState(false);
    const [success, setSuccess] = useState(false);
    const [error, setError] = useState("");

    const isRTL = locale === "ar";

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setForm({ ...form, [e.target.name]: e.target.value });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            setFiles(Array.from(e.target.files));
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError("");
        setSuccess(false);
        try {
            const formData = new FormData();
            formData.append("name", form.name);
            formData.append("email", form.email);
            formData.append("phone", form.phone);
            formData.append("organization", form.organization);
            formData.append("message", form.message);
            files.forEach((file, idx) => {
                formData.append("delegationDocs", file);
            });
            const res = await fetchApi("/api/general/permit-request", {
                method: "POST",
                headers: { "X-localization": locale },
                body: formData,
            });
            const text = await res.text();
            let data: any = {};
            try {
                data = text ? JSON.parse(text) : {};
            } catch { }
            if (res.ok) {
                setSuccess(true);
                setError("");
                setForm({ name: "", email: "", phone: "", organization: "", message: "" });
                setFiles([]);
            } else {
                setError(data.message || "Failed to send request");
                setSuccess(false);
            }
        } catch (err: any) {
            setError(err.message || "Unknown error");
            setSuccess(false);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-[#f5f5f5] mb-[-5%] pb-24">
            <div className="mx-auto bg-white rounded-lg shadow p-0 md:p-0">
                {/* Header image and title */}
                <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
                    <Image
                        src="/img/default_bg.png"
                        alt="stadium"
                        fill
                        className="object-cover w-full h-full"
                        priority
                    />
                    <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
                        <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
                            {t("permit_request")}
                        </h2>
                    </div>
                </div>
                {/* Permit Request Form */}
                <div className="bg-[#f5f5f5] py-10 px-2 md:px-10">
                    <div className={`flex flex-col md:flex-row gap-8 justify-center items-start mt-10 w-full`}>
                        {/* Form */}
                        <form
                            onSubmit={handleSubmit}
                            className={`flex flex-col md:flex-row w-full max-w-4xl mx-auto gap-0 md:gap-8 bg-white rounded-lg shadow-lg border border-[#eee]`}
                            dir={isRTL ? 'rtl' : 'ltr'}
                            style={{ minHeight: 260 }}
                        >
                            {/* Right: fields (RTL: right, LTR: left) */}
                            <div className="flex-1 flex flex-col gap-6 p-4 md:p-8 bg-white rounded-t-lg md:rounded-t-none md:rounded-e-lg">
                                <div className="flex flex-col gap-4">
                                    <input
                                        name="name"
                                        value={form.name}
                                        onChange={handleChange}
                                        placeholder={t("name")}
                                        className={`w-full p-3 border border-gray-300 rounded focus:outline-none focus:border-[#C60428] bg-gray-50 ${isRTL ? 'text-right' : 'text-left'} text-base`}
                                        required
                                    />
                                    <input
                                        name="email"
                                        type="email"
                                        value={form.email}
                                        onChange={handleChange}
                                        placeholder={t("email")}
                                        className={`w-full p-3 border border-gray-300 rounded focus:outline-none focus:border-[#C60428] bg-gray-50 ${isRTL ? 'text-right' : 'text-left'} text-base`}
                                        required
                                    />
                                    <input
                                        name="phone"
                                        value={form.phone}
                                        onChange={handleChange}
                                        placeholder={t("phone")}
                                        className={`w-full p-3 border border-gray-300 rounded focus:outline-none focus:border-[#C60428] bg-gray-50 ${isRTL ? 'text-right' : 'text-left'} text-base`}
                                    />
                                    <input
                                        name="organization"
                                        value={form.organization}
                                        onChange={handleChange}
                                        placeholder={t("organization") || "جهة العمل"}
                                        className={`w-full p-3 border border-gray-300 rounded focus:outline-none focus:border-[#C60428] bg-gray-50 ${isRTL ? 'text-right' : 'text-left'} text-base`}
                                    />
                                    {/* File upload for delegation documents */}
                                    <label className="w-full">
                                        <span className="block mb-2 font-semibold text-[#222]">{t("attach_delegation_docs")}</span>
                                        <input
                                            type="file"
                                            name="delegationDocs"
                                            accept="image/*,application/pdf"
                                            multiple
                                            onChange={handleFileChange}
                                            className="block w-full text-base text-gray-700 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-[#C60428] file:text-white hover:file:bg-[#a0021f]"
                                        />
                                    </label>
                                </div>
                            </div>
                            {/* Left: textarea (RTL: left, LTR: right) */}
                            <div className="flex-1 flex flex-col p-4 md:p-8 border-b md:border-b-0 md:border-l md:border-r-0 border-[#eee] bg-[#fafafa] rounded-b-lg md:rounded-b-none md:rounded-s-lg">
                                <textarea
                                    name="message"
                                    value={form.message}
                                    onChange={handleChange}
                                    placeholder={t("your_message")}
                                    className={`w-full h-40 md:h-56 p-4 border border-gray-300 rounded focus:outline-none focus:border-[#C60428] resize-none bg-gray-50 ${isRTL ? 'text-right' : 'text-left'} text-base`}
                                    required
                                    style={{ minHeight: 180 }}
                                />
                                <div className="flex flex-col items-center gap-2 mt-4">
                                    <button
                                        type="submit"
                                        className="bg-[#C60428] text-white font-bold px-8 py-2 rounded hover:bg-[#a0021f] transition disabled:opacity-60 w-full text-lg"
                                        disabled={loading}
                                        style={{ minHeight: 44 }}
                                    >
                                        {loading ? t("sending") : t("permit_request")}
                                    </button>
                                    {success && <div className="text-green-600 font-semibold">{t("success_message")}</div>}
                                    {error && <div className="text-red-600 font-semibold">{t("error_message")}</div>}
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    );
}
