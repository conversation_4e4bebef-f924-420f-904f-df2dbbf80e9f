"use client";
import { fetchApi } from "@/lib/api";
import translations from "@/lib/locales";
import { useState } from "react";
import Image from "next/image";

interface ContactUsPageProps {
    locale: string;
}

export default function ContactUsPage({ locale }: ContactUsPageProps) {
    const t = (key: string) => translations[locale]?.[key] || key;
    const [form, setForm] = useState({
        name: "",
        email: "",
        phone: "",
        message: "",
    });
    const [loading, setLoading] = useState(false);
    const [success, setSuccess] = useState(false);
    const [error, setError] = useState("");

    const isRTL = locale === "ar";

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setForm({ ...form, [e.target.name]: e.target.value });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setError("");
        setSuccess(false);
        try {
            const res = await fetchApi("/api/general/contact-us", {
                method: "POST",
                headers: { "Content-Type": "application/json", "X-localization": locale },
                body: JSON.stringify(form),
            });
            // Ensure we always await the response and parse it
            const text = await res.text();
            let data: any = {};
            try {
                data = text ? JSON.parse(text) : {};
            } catch { }
            if (res.ok) {
                setSuccess(true);
                setError("");
                setForm({ name: "", email: "", phone: "", message: "" });
            } else {
                setError(data.message || "Failed to send message");
                setSuccess(false);
            }
        } catch (err: any) {
            setError(err.message || "Unknown error");
            setSuccess(false);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-[#f5f5f5] mb-[-5%] pb-24">
            <div className="mx-auto bg-white rounded-lg shadow p-0 md:p-0">
                {/* Header image and title */}
                <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
                    <Image
                        src="/img/default_bg.png"
                        alt="stadium"
                        fill
                        className="object-cover w-full h-full"
                        priority
                    />
                    <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
                        <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
                            {t("contact_us_title")}
                        </h2>
                    </div>
                </div>
                {/* Hotline */}
                <div className="bg-[#f5f5f5] py-10 px-2 md:px-10">
                    <h2 className="text-3xl md:text-4xl font-bold text-center mb-2 text-[#222]">
                        <span className="text-[#222]">{t("hotline")}</span> <span className="text-[#C60428]">101907</span>
                    </h2>
                    <div className={`flex flex-col md:flex-row gap-8 justify-center items-start mt-10 w-full`}>
                        {/* Form */}
                        <form
                            onSubmit={handleSubmit}
                            className={`flex flex-col md:flex-row w-full max-w-4xl mx-auto gap-0 md:gap-8 bg-white rounded-lg shadow-lg border border-[#eee]`}
                            dir={isRTL ? 'rtl' : 'ltr'}
                            style={{ minHeight: 260 }}
                        >
                            {/* Right: fields (RTL: right, LTR: left) */}
                            <div className="flex-1 flex flex-col gap-6 p-4 md:p-8 bg-white rounded-t-lg md:rounded-t-none md:rounded-e-lg">
                                <div className="text-lg font-bold text-[#222] mb-4 whitespace-nowrap">{t("send_message")}</div>
                                <div className="flex flex-col gap-4">
                                    <input
                                        name="name"
                                        value={form.name}
                                        onChange={handleChange}
                                        placeholder={t("name")}
                                        className={`w-full p-3 border border-gray-300 rounded focus:outline-none focus:border-[#C60428] bg-gray-50 ${isRTL ? 'text-right' : 'text-left'} text-base`}
                                        required
                                    />
                                    <input
                                        name="email"
                                        type="email"
                                        value={form.email}
                                        onChange={handleChange}
                                        placeholder={t("email")}
                                        className={`w-full p-3 border border-gray-300 rounded focus:outline-none focus:border-[#C60428] bg-gray-50 ${isRTL ? 'text-right' : 'text-left'} text-base`}
                                        required
                                    />
                                    <input
                                        name="phone"
                                        value={form.phone}
                                        onChange={handleChange}
                                        placeholder={t("phone")}
                                        className={`w-full p-3 border border-gray-300 rounded focus:outline-none focus:border-[#C60428] bg-gray-50 ${isRTL ? 'text-right' : 'text-left'} text-base`}
                                    />
                                </div>
                            </div>
                            {/* Left: textarea (RTL: left, LTR: right) */}
                            <div className="flex-1 flex flex-col p-4 md:p-8 border-b md:border-b-0 md:border-l md:border-r-0 border-[#eee] bg-[#fafafa] rounded-b-lg md:rounded-b-none md:rounded-s-lg">
                                <textarea
                                    name="message"
                                    value={form.message}
                                    onChange={handleChange}
                                    placeholder={t("your_message")}
                                    className={`w-full h-40 md:h-56 p-4 border border-gray-300 rounded focus:outline-none focus:border-[#C60428] resize-none bg-gray-50 ${isRTL ? 'text-right' : 'text-left'} text-base`}
                                    required
                                    style={{ minHeight: 180 }}
                                />

                                <div className="flex flex-col items-center gap-2 mt-4">
                                    <button
                                        type="submit"
                                        className="bg-[#C60428] text-white font-bold px-8 py-2 rounded hover:bg-[#a0021f] transition disabled:opacity-60 w-full text-lg"
                                        disabled={loading}
                                        style={{ minHeight: 44 }}
                                    >
                                        {loading ? t("sending") : t("send")}
                                    </button>
                                    {success && <div className="text-green-600 font-semibold">{t("success_message")}</div>}
                                    {error && <div className="text-red-600 font-semibold">{t("error_message")}</div>}
                                </div>
                            </div>
                        </form>
                    </div>
                    {/* Side title for mobile */}
                    <div className="block md:hidden text-center mt-8">
                        <div className="text-lg font-bold text-[#222]">{t("send_message")}</div>
                    </div>
                </div>
            </div>
        </div>
    );
}
