import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import translations from '@/lib/locales';

export interface NewsItem {
    id: number;
    title: string;
    slug: string;
    intro: string;
    image: string;
    image_full: string;
    video_url: string;
    published_at: string;
    category?: {
        title: string;
    };
    gallery?: { id: number; news_id: number; image: string }[];
}

export default function NewsCard({ item, locale }: { item: NewsItem; locale: string; }) {
    const [imgLoading, setImgLoading] = useState(true);
    const [imgSrc, setImgSrc] = useState(item.image_full || '/img/default_placeholder.png');
    return (
        <Link
            key={item.id}
            href={`/${locale}/club/news/${item.slug}`}
            className="group relative bg-white rounded-[5px] shadow-[0px_4px_10px_rgba(0,0,0,0.1)] hover:shadow-lg transition-all overflow-hidden flex flex-col h-[345px]"
        >
            {/* Image Section */}
            <div className="relative h-[225px] overflow-hidden">
                <Image
                    src={imgSrc}
                    alt={item.title}
                    fill
                    className={`object-cover group-hover:scale-105 transition-transform ${imgLoading ? 'opacity-50' : 'opacity-100'}`}
                    onError={() => setImgSrc('/img/default_placeholder.png')}
                    onLoadingComplete={() => setImgLoading(false)}
                />
                {imgLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white/60 z-10">
                        <svg className="animate-spin h-8 w-8 text-[#C60428]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                        </svg>
                    </div>
                )}
                {/* Image Overlay Elements */}
                <div className="absolute top-4 left-4 flex flex-col">
                    <span className="font-cairo text-2xl font-black text-white">
                        {new Date(item.published_at).getDate()}
                    </span>
                    <span className="text-[10px] text-white">
                        {new Date(item.published_at).toLocaleString(locale.replace('_', '-'), { month: 'long' }) || 'ديسمبر'}
                    </span>
                </div>
                <div className="absolute top-[179px] right-4 bg-[#D10128] px-4 py-1 rounded">
                    <span className="text-xs text-white">
                        {item.category?.title}
                    </span>
                </div>
                <div className="absolute bottom-2 left-4 flex items-center gap-2 text-white text-xs">
                    <span>{item.published_at?.split('T')[1]?.slice(0, 5)}</span>
                    <span className="w-1 h-1 bg-white rounded-full" />
                </div>
            </div>
            {/* Content Section */}
            <div className="flex-1 p-4 flex flex-col justify-between">
                <h3 className="font-hacen-algeria-bd text-[20px] leading-[150%] text-[#272525] text-start truncate whitespace-nowrap overflow-hidden">
                    {item.title}
                </h3>
                <div className="border-t border-[#E0E0E0] pt-3">
                    <button className="text-[#D10128] hover:text-red-800 text-sm flex items-center gap-1 float-end cursor-pointer">
                        {translations[locale]?.details || 'التفاصيل'}
                        <svg
                            width="12"
                            height="12"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M7.29297 14.707C7.10547 14.8945 7.00001 15.1488 7.00001 15.414C7.00001 15.6792 7.10547 15.9335 7.29297 16.121C7.48051 16.3085 7.73482 16.414 8.00001 16.414C8.26521 16.414 8.51951 16.3085 8.70701 16.121L12.707 12.121C12.8945 11.9335 13 11.6792 13 11.414C13 11.1488 12.8945 10.8945 12.707 10.707L8.70701 6.70703C8.51845 6.52222 8.2658 6.41943 8.00361 6.42133C7.74142 6.42324 7.49062 6.52971 7.3053 6.71754C7.11997 6.90538 7.0149 7.15912 7.013 7.42131C7.01109 7.6835 7.11251 7.93613 7.29297 8.12103L10.586 11.414L7.29297 14.707Z"
                                fill="currentColor"
                            />
                        </svg>
                    </button>
                </div>
            </div>
        </Link>
    );
}
