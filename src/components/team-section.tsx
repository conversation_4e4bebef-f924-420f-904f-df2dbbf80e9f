"use client";

import Image from "next/image";
import { useState, useEffect } from "react";
import translations from "@/lib/locales";
import { usePathname } from "next/navigation";
import Modal from "@/components/Modal";
import { fetchApi } from "@/lib/api";

export type Player = {
  id: number;
  slug: string;
  name: string;
  image?: string | null; // allow undefined/null
  position?: string; // optional position field
};

export default function TeamSection({ players }: { players: Player[] }) {
  const [activeIndex, setActiveIndex] = useState(
    Math.floor(players.length / 2)
  );
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isClient, setIsClient] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalPlayer, setModalPlayer] = useState<any | null>(null);
  const [modalLoading, setModalLoading] = useState(false);
  const [modalError, setModalError] = useState<string | null>(null);

  const pathname = usePathname();
  const locale = pathname ? pathname.split("/")[1] : "ar";
  const t = (key: string) => translations[locale]?.[key] || key;

  useEffect(() => {
    setIsClient(true);
  }, []);

  const next = () => {
    setActiveIndex((prev) => (prev + 1) % players.length);
    setIsAutoPlaying(false);
  };

  const prev = () => {
    setActiveIndex((prev) => (prev - 1 + players.length) % players.length);
    setIsAutoPlaying(false);
  };

  const getStyleForPlayer = (index: number) => {
    let diff = index - activeIndex;
    const half = Math.floor(players.length / 2);

    if (diff > half) diff -= players.length;
    if (diff < -half) diff += players.length;

    const distance = Math.abs(diff);

    let scale = 1;
    let opacity = 1;
    let zIndex = 30;
    let translateX = 0;

    if (isClient) {
      translateX = diff * (window.innerWidth >= 768 ? 300 : 140);
      if (index === activeIndex) {
        scale = window.innerWidth >= 768 ? 1.25 : 1.15;
        zIndex = 50;
        opacity = 1;
        translateX = 0;
      } else if (distance <= 3) {
        scale = 1 - distance * 0.03;
        opacity = 1 - distance * 0.15;
        zIndex = 30 - distance * 5;
      } else {
        scale = 0.8;
        opacity = 0;
        zIndex = 0;
      }
    }

    // On the server, always return the centered style for hydration match
    if (!isClient) {
      scale = 1;
      opacity = 1;
      zIndex = 30;
      translateX = 0;
    }

    return {
      position: "absolute" as const,
      left: "50%",
      top: "50%",
      transform: `translateX(calc(${translateX}px - 50%)) translateY(-50%) scale(${scale})`,
      opacity,
      zIndex,
      transition: "transform 0.5s ease, opacity 0.5s ease",
      transformOrigin: "center center",
      width: isClient && window.innerWidth >= 768 ? "240px" : "180px",
      display: opacity === 0 ? "none" : "flex",
      flexDirection: opacity === 0 ? undefined : "column" as const,
      alignItems: opacity === 0 ? undefined : "center" as const,
      justifyContent: opacity === 0 ? undefined : "flex-end" as const,
    } as React.CSSProperties;
  };

  useEffect(() => {
    if (!isAutoPlaying) return;
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % players.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, players.length]);

  async function handlePlayerClick(slug: string) {
    setModalOpen(true);
    setModalLoading(true);
    setModalError(null);
    setModalPlayer(null);
    try {
      const data = await fetchApi(
        `/api/sports/players/${slug}`,
        { headers: { "X-localization": locale } }
      );
      if (data.status && data.data) {
        setModalPlayer(data.data);
      } else {
        setModalError(t("no_player"));
      }
    } catch (err) {
      setModalError(t("no_player"));
    } finally {
      setModalLoading(false);
    }
  }

  return (
    <div className="w-full relative">
      <div className="relative w-full h-[450px] md:h-[550px] mx-auto mt-8 md:mt-10 overflow-hidden">
        <div className="relative w-full h-full">
          {players.map((player, index) => {
            const styles = getStyleForPlayer(index);
            return (
              <div key={`${player.id}-${index}`} style={styles}>
                <button
                  onClick={() => handlePlayerClick(player.slug)}
                  className="focus:outline-none group"
                  style={{
                    background: "none",
                    border: "none",
                    padding: 0,
                    margin: 0,
                    cursor: "pointer"
                  }}
                  tabIndex={0}
                >
                  {player.image ? (
                    <Image
                      src={player.image}
                      alt={player.name}
                      width={240}
                      height={320}
                      className="object-cover rounded-md h-[240px] w-[180px] md:h-[320px] md:w-[240px] group-hover:shadow-lg group-hover:scale-105 transition-transform duration-200"
                    />
                  ) : (
                    <div className="flex items-center justify-center bg-gray-200 text-gray-500 h-[240px] w-[180px] md:h-[320px] md:w-[240px] rounded-md group-hover:shadow-lg group-hover:scale-105 transition-transform duration-200">
                      {t("no_image")}
                    </div>
                  )}
                  <div
                    className={`mt-3 text-center transition-opacity duration-300 ${
                      index === activeIndex ? "opacity-100" : "opacity-0"
                    }`}
                  >
                    <p className="text-sm md:text-lg font-bold text-[#DEB65D]">
                      {player.name}
                    </p>
                    <p className="text-white text-xs md:text-sm">
                      {player.position}
                    </p>
                  </div>
                </button>
              </div>
            );
          })}
        </div>

        <button
          onClick={locale === "ar" ? next : prev}
          className="absolute cursor-pointer right-[10%] bottom-10 md:right-[25%] md:bottom-12 z-40 text-white px-3 py-1 md:px-4 md:py-2 rounded-lg hover:text-[#C60428] transition-colors duration-300 text-3xl"
        >
          {locale === "ar" ? "❮" : "❯"}
        </button>

        <button
          onClick={locale === "ar" ? prev : next}
          className="absolute cursor-pointer left-[10%] bottom-10 md:left-[25%] md:bottom-12 z-40 text-white px-3 py-1 md:px-4 md:py-2 rounded-lg hover:text-[#C60428] transition-colors duration-300 text-3xl"
        >
          {locale === "ar" ? "❯" : "❮"}
        </button>
      </div>
      {/* Player Details Modal */}
      {modalOpen && (
        <Modal onClose={() => setModalOpen(false)}>
          <div className="flex flex-col md:flex-row-reverse gap-0 w-[90vw] max-w-4xl h-[600px]">
            {/* Info and bio */}
            <div className="flex-1 bg-[#222] p-8 flex flex-col justify-start min-w-0 h-full overflow-y-scroll rounded-e-2xl custom-scrollbar">
              {modalLoading ? (
                <div className="text-center text-[#C60428] py-10 text-lg font-bold">
                  {t("loading")}
                </div>
              ) : modalError ? (
                <div className="text-center text-red-600 py-10 text-lg font-bold">
                  {modalError}
                </div>
              ) : modalPlayer ? (
                <>
                  <h2 className="text-3xl font-bold text-[#e6c97b] mb-6 text-start">
                    {modalPlayer.name}
                  </h2>
                  <table className="w-full text-white mb-6">
                    <tbody>
                      <tr>
                        <td className="py-1 pe-4 text-[#e6c97b] font-bold">
                          {t("position")}
                        </td>
                        <td className="py-1">{modalPlayer.position}</td>
                      </tr>
                      {/* Add more fields as needed, e.g. age, height, weight if available */}
                    </tbody>
                  </table>
                  {/* Bio if available */}
                  {modalPlayer.bio && (
                    <div className="text-white text-base leading-8 text-start whitespace-pre-line max-h-[120px] overflow-y-scroll custom-scrollbar mb-4">
                      {modalPlayer.bio}
                    </div>
                  )}
                  {/* Career Table */}
                  <div className="mt-4">
                    <h3 className="text-[#e6c97b] text-lg font-bold mb-2">
                      {t("career_history")}
                    </h3>
                    <table className="w-full text-white text-sm mb-4 border-separate border-spacing-y-2">
                      <thead>
                        <tr className="bg-[#333]">
                          <th className="py-1 px-2 text-start">{t("club")}</th>
                          <th className="py-1 px-2 text-center">{t("season")}</th>
                          <th className="py-1 px-2 text-center">{t("appearances")}</th>
                          <th className="py-1 px-2 text-center">{t("goals")}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {modalPlayer.careers && modalPlayer.careers.length > 0 ? (
                          modalPlayer.careers.map((c: any) => (
                            <tr key={c.id} className="bg-[#222]">
                              <td className="py-1 px-2 text-start">{c.club}</td>
                              <td className="py-1 px-2 text-center">{c.season}</td>
                              <td className="py-1 px-2 text-center">{c.appearances}</td>
                              <td className="py-1 px-2 text-center">{c.goals}</td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={4} className="text-center py-2">
                              {t("no_career_data")}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                  {/* National Stats Table */}
                  <div className="mt-4">
                    <h3 className="text-[#e6c97b] text-lg font-bold mb-2">
                      {t("national_team")}
                    </h3>
                    <table className="w-full text-white text-sm mb-4 border-separate border-spacing-y-2">
                      <thead>
                        <tr className="bg-[#333]">
                          <th className="py-1 px-2 text-center">{t("appearances")}</th>
                          <th className="py-1 px-2 text-center">{t("goals")}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {modalPlayer.national_stats &&
                          modalPlayer.national_stats.length > 0 ? (
                          modalPlayer.national_stats.map((n: any) => (
                            <tr key={n.id} className="bg-[#222]">
                              <td className="py-1 px-2 text-center">{n.appearances}</td>
                              <td className="py-1 px-2 text-center">{n.goals}</td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={2} className="text-center py-2">
                              {t("no_national_data")}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </>
              ) : null}
            </div>
            {/* Player Image */}
            <div className="w-full md:w-[350px] flex-shrink-0 bg-[#e5e5e5] flex items-center justify-center h-full rounded-s-2xl">
              {modalPlayer && modalPlayer.image ? (
                <Image
                  src={modalPlayer.image}
                  alt={modalPlayer.name}
                  width={220}
                  height={320}
                  className="rounded-md object-cover border-4 border-[#fff] shadow"
                  style={{ background: "#ddd" }}
                />
              ) : (
                <div className="w-[220px] h-[320px] rounded-md bg-[#ddd] flex items-center justify-center">
                  <svg
                    width="100"
                    height="100"
                    fill="#bbb"
                    viewBox="0 0 24 24"
                  >
                    <circle cx="12" cy="8" r="4" />
                    <path d="M12 14c-4.418 0-8 1.79-8 4v2h16v-2c0-2.21-3.582-4-8-4z" />
                  </svg>
                </div>
              )}
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}
