"use client";
import NewsCard from "@/components/news-card";
import { useRef, useState, useEffect } from "react";

const CARDS_PER_PAGE = 3;
const CARD_WIDTH = 320; // px
const CARD_GAP = 24; // px (gap-6)

export default function RelatedNewsCarousel({ news, locale, relatedNewsTitle, noNewsText }: { news: any[]; locale: string; relatedNewsTitle: string; noNewsText: string }) {
    const carouselRef = useRef<HTMLDivElement>(null);
    const [page, setPage] = useState(0);
    const totalPages = Math.ceil(news.length / CARDS_PER_PAGE);
    const isRTL = locale === "ar" || locale === "he" || locale === "fa";

    // Scroll to a page
    const scrollToPage = (pageIdx: number) => {
        if (carouselRef.current) {
            const container = carouselRef.current;
            const pageWidth = (CARD_WIDTH + CARD_GAP) * CARDS_PER_PAGE;
            if (isRTL) {
                // For RTL, scroll from the right edge (invert the page index)
                const maxScroll = container.scrollWidth - container.clientWidth;
                container.scrollTo({ left: maxScroll - (totalPages - 1 - pageIdx) * pageWidth, behavior: "smooth" });
            } else {
                container.scrollTo({ left: pageIdx * pageWidth, behavior: "smooth" });
            }
        }
        setPage(pageIdx);
    };

    // Arrow navigation
    const handlePrev = () => {
        if (page > 0) scrollToPage(page - 1);
    };
    const handleNext = () => {
        if (page < totalPages - 1) scrollToPage(page + 1);
    };

    // Handle manual scroll (sync page state)
    const handleScroll = () => {
        if (carouselRef.current) {
            const container = carouselRef.current;
            const pageWidth = (CARD_WIDTH + CARD_GAP) * CARDS_PER_PAGE;
            let scrollLeft = container.scrollLeft;
            if (isRTL) {
                const maxScroll = container.scrollWidth - container.clientWidth;
                // Invert the scrollLeft for RTL
                scrollLeft = maxScroll - scrollLeft;
                // Calculate the page index from the right
                const pageIdx = totalPages - 1 - Math.round(scrollLeft / pageWidth);
                setPage(pageIdx);
            } else {
                const pageIdx = Math.round(scrollLeft / pageWidth);
                setPage(pageIdx);
            }
        }
    };

    // Set scroll direction on mount
    useEffect(() => {
        if (carouselRef.current) {
            carouselRef.current.setAttribute('dir', isRTL ? 'rtl' : 'ltr');
            // Ensure carousel starts at the correct edge for RTL/LTR
            const container = carouselRef.current;
            const pageWidth = (CARD_WIDTH + CARD_GAP) * CARDS_PER_PAGE;
            if (isRTL) {
                const maxScroll = container.scrollWidth - container.clientWidth;
                container.scrollLeft = maxScroll;
            } else {
                container.scrollLeft = 0;
            }
        }
        setPage(0);
    }, [isRTL, news.length]);

    return (
        <section className="w-full p-4 sm:p-6 md:p-8 text-white">
            {/* Pagination dots and title row */}
            <div className={`flex items-center justify-between mb-4 px-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <h5 className={`text-xl sm:text-2xl md:text-3xl ${isRTL ? 'order-2' : 'order-1'}`}>{relatedNewsTitle}</h5>
                <div className={`flex items-center gap-3 ${isRTL ? 'order-1' : 'order-2'}`}> {/* Dots */}
                    {totalPages > 1 && (
                        <div className="flex gap-2">
                            {Array.from({ length: totalPages }).map((_, idx) => (
                                <button
                                    key={idx}
                                    aria-label={`Go to page ${idx + 1}`}
                                    className={`w-3 h-3 rounded-full border-2 border-[#C60428] transition-all duration-200 ${page === idx ? "bg-[#C60428]" : "bg-white"}`}
                                    onClick={() => scrollToPage(idx)}
                                />
                            ))}
                        </div>
                    )}
                </div>
            </div>
            <div className="relative">
                {/* Carousel Arrows */}
                {/* LTR: Prev (left), Next (right). RTL: Prev (right), Next (left) */}
                {/* Next Button */}
                <button
                    type="button"
                    aria-label={isRTL ? "التالي" : "Next"}
                    onClick={handleNext}
                    disabled={page === totalPages - 1}
                    className={`absolute ${isRTL ? '-left-6' : '-right-6'} top-1/2 -translate-y-1/2 z-10 bg-[#C60428] hover:bg-[#a3001e] text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg border-2 border-white transition-all duration-200 ${page === totalPages - 1 ? "opacity-40 cursor-not-allowed" : ""}`}
                    style={{ direction: isRTL ? 'rtl' : 'ltr' }}
                >
                    <span className="text-3xl">{isRTL ? '\u203a' : '\u203a'}</span>
                </button>
                {/* Prev Button */}
                <button
                    type="button"
                    aria-label={isRTL ? "السابق" : "Previous"}
                    onClick={handlePrev}
                    disabled={page === 0}
                    className={`absolute ${isRTL ? '-right-6' : '-left-6'} top-1/2 -translate-y-1/2 z-10 bg-[#C60428] hover:bg-[#a3001e] text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg border-2 border-white transition-all duration-200 ${page === 0 ? "opacity-40 cursor-not-allowed" : ""}`}
                    style={{ direction: isRTL ? 'rtl' : 'ltr' }}
                >
                    <span className="text-3xl">{isRTL ? '\u2039' : '\u2039'}</span>
                </button>
                {/* Carousel Container */}
                <div
                    ref={carouselRef}
                    id="related-news-carousel"
                    className={`flex gap-6 overflow-x-auto scrollbar-hide scroll-smooth px-2 py-2 justify-center ${isRTL ? 'flex-row-reverse' : ''}`}
                    style={{ scrollSnapType: 'x mandatory', direction: isRTL ? 'rtl' : 'ltr' }}
                    onScroll={handleScroll}
                >
                    {news.length > 0 ? news.map((item: any, i: number) => (
                        <div
                            key={item.id}
                            className="min-w-[300px] max-w-[320px] flex-shrink-0 scroll-snap-align-start"
                            style={{ scrollSnapAlign: 'center' }}
                        >
                            <NewsCard item={item} locale={locale} />
                        </div>
                    )) : <div className="col-span-4 text-center text-gray-400">{noNewsText}</div>}
                </div>
            </div>
        </section>
    );
}
