"use client";

import { fetchApi } from '@/lib/api';
import React, { createContext, useContext, useEffect, useState } from "react";

interface GlobalData {
  sports: any[];
  partners: any[];
  top_banner?: {
    id: number;
    title: string;
    image: string;
    url?: string;
    placement?: string;
  };
  loading: boolean;
}

const GlobalDataContext = createContext<GlobalData>({ sports: [], partners: [], loading: true });

export function GlobalDataProvider({ children }: { children: React.ReactNode }) {
  const [sports, setSports] = useState<any[]>([]);
  const [partners, setPartners] = useState<any[]>([]);
  const [topBanner, setTopBanner] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const locale = typeof window !== 'undefined' ? (window.location.pathname.split("/")[1] || "ar") : "ar";
    const cacheKey = `globalData_${locale}`;
    const cacheTimeKey = `globalDataTime_${locale}`;
    const now = Date.now();
    const THIRTY_MIN = 30 * 60 * 1000;

    setLoading(true); // Always set loading true at the start of effect

    let didLoad = false;
    // Try to load from cache
    const cached = typeof window !== 'undefined' ? localStorage.getItem(cacheKey) : null;
    const cachedTime = typeof window !== 'undefined' ? parseInt(localStorage.getItem(cacheTimeKey) || '0', 10) : 0;
    let shouldRefresh = true;
    if (cached && cachedTime && now - cachedTime < THIRTY_MIN) {
      try {
        const parsed = JSON.parse(cached);
        if (Array.isArray(parsed.sports)) setSports(parsed.sports);
        if (Array.isArray(parsed.partners)) setPartners(parsed.partners);
        if (parsed.top_banner) setTopBanner(parsed.top_banner);
        shouldRefresh = false;
        didLoad = true;
      } catch {}
    }

    if (shouldRefresh) {
      fetchApi("/api/widgets/footer", {
        headers: { 'X-localization': locale },
      })
        .then(data => {
          if (Array.isArray(data.sports)) setSports(data.sports);
          if (Array.isArray(data.partners)) setPartners(data.partners);
          if (data.top_banner) setTopBanner(data.top_banner);
          if (typeof window !== 'undefined') {
            localStorage.setItem(cacheKey, JSON.stringify({ sports: data.sports, partners: data.partners, top_banner: data.top_banner }));
            localStorage.setItem(cacheTimeKey, now.toString());
          }
        })
        .finally(() => setLoading(false));
    } else {
      setLoading(false);
    }

    // Set up interval to refresh every 30 minutes
    const interval = setInterval(() => {
      fetchApi("/api/widgets/footer", {
        headers: { 'X-localization': locale },
      })
        .then(data => {
          if (Array.isArray(data.sports)) setSports(data.sports);
          if (Array.isArray(data.partners)) setPartners(data.partners);
          if (data.top_banner) setTopBanner(data.top_banner);
          if (typeof window !== 'undefined') {
            localStorage.setItem(cacheKey, JSON.stringify({ sports: data.sports, partners: data.partners, top_banner: data.top_banner }));
            localStorage.setItem(cacheTimeKey, Date.now().toString());
          }
        })
        .catch(() => {});
    }, 30 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  return (
    <GlobalDataContext.Provider value={{ sports, partners, top_banner: topBanner, loading }}>
      {children}
    </GlobalDataContext.Provider>
  );
}

export function useGlobalData() {
  return useContext(GlobalDataContext);
}
