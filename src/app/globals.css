@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@font-face {
  font-family: "Hacen Algeria";
  src: url("/fonts/Hacen Algeria Bd Regular.ttf") format("truetype");
  font-display: block;
}

* {
  font-family: "Hacen Algeria";
  src: url("/fonts/Hacen Algeria Bd Regular.ttf") format("truetype");

  font-display: block;
}

.no-max-width {
  max-width: none !important;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-kufi: var(--font-kufi);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --font-kufi: "Hacen Tunisia", sans-serif;
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

html[dir="rtl"] body {
  font-family: "Hacen Tunisia", sans-serif;
}

html[dir="ltr"] body {
  font-family: Arial, Helvetica, sans-serif;
}

.carousel-cell {
  margin-right: 10px;
  width: 240px;
  height: 160px;
  border-radius: 8px;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.scrollbar-hidden {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* IE/Edge */
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Custom scrollbar for modal and other scrollable elements - Always visible */
.custom-scrollbar {
  /* Firefox */
  scrollbar-width: thin !important;
  scrollbar-color: #e6c97b #333 !important;
  /* Force scrollbar to always show */
  overflow-y: scroll !important;
}

/* Webkit browsers (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 12px !important;
  display: block !important;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #333 !important;
  border-radius: 6px !important;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #e6c97b !important;
  border-radius: 6px !important;
  border: 2px solid #333 !important;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #d4b867 !important;
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: #333 !important;
}

/* Make images inside API content responsive and not floating */
.news-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1rem auto;
  float: none !important;
}

/* Responsive and visible iframes inside news/article content */
.prose iframe,
.prose p > iframe {
  display: block;
  max-width: 100%;
  width: 100%;
  height: auto;
  aspect-ratio: 16/9;
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  background: #000;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .animate-shimmer {
    animation: shimmer 1.5s linear infinite;
    background-size: 200% 100%;
    background-repeat: no-repeat;
  }
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* ظل مخصص ثلاثي الأبعاد خفيف لشعار الأهلي (Light 3D Drop shadow for Al Ahly logo) */
.drop-shadow-ahly-logo {
  /* ظل خفيف أسفل الشعار فقط بدون انتشار كبير */
  filter:
    drop-shadow(0 6px 8px rgba(0,0,0,0.13)) /* ظل سفلي صغير */;
  /* يمكن تعديل الشفافية أو الاتجاه حسب الحاجة */
}

/* Search Modal Specific Styles */
.search-input::placeholder {
  color: #9CA3AF; /* gray-400 */
  opacity: 1;
  font-weight: 400;
}

.search-input:focus::placeholder {
  color: #D1D5DB; /* lighter gray when focused */
}

.search-input {
  color: #111827; /* gray-900 - ensure text is always dark */
}

.search-input:focus {
  color: #111827; /* gray-900 - maintain dark color on focus */
}

/* Enhanced search result hover effects */
.search-result-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-result-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(198, 4, 40, 0.08);
}

/* Ensure search result text is not transparent */
.search-result-item p {
  opacity: 1 !important;
  color: inherit !important;
}

.search-result-item .search-title {
  color: #111827 !important; /* gray-900 */
  opacity: 1 !important;
}

.search-result-item .search-intro {
  color: #4B5563 !important; /* gray-600 */
  opacity: 1 !important;
}

/* Search loading spinner with Al Ahly branding */
.search-spinner {
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: #C60428;
  border-left-color: #C60428;
}

/* Quick link buttons with enhanced hover effects */
.quick-link-btn {
  transition: all 0.2s ease-in-out;
}

.quick-link-btn:hover {
  box-shadow: 0 2px 8px rgba(198, 4, 40, 0.2);
}

/* Ensure all search-related text is properly visible */
.search-modal-content h1,
.search-modal-content h2,
.search-modal-content h3,
.search-modal-content h4,
.search-modal-content h5,
.search-modal-content h6,
.search-modal-content p,
.search-modal-content span,
.search-modal-content div {
  color: inherit; /* Prevent unwanted color inheritance */
  opacity: 1 !important; /* Force full opacity */
}

/* Force search result visibility */
.search-modal-content .search-result-item * {
  opacity: 1 !important;
}

.search-page-content h1,
.search-page-content h2,
.search-page-content h3,
.search-page-content h4,
.search-page-content h5,
.search-page-content h6 {
  color: #111827; /* gray-900 for maximum readability */
  opacity: 1 !important;
}