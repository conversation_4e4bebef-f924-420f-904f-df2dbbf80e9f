import "../../globals.css";
import { dir } from 'i18next';
import { use } from 'react';

const defaultLocale = 'ar';


export default function LoginLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode;
  params: any
}>) {
  // Support both sync and async params (for Next.js 15+)
  const resolvedParams = typeof params?.then === 'function' ? use(params) : params;
  const locale = resolvedParams?.locale || defaultLocale;
  return (
    <html lang={locale} dir={dir(locale)}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, user-scalable=no" />
        <meta name="theme-color" content="#C60428" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
        <meta name="format-detection" content="telephone=no" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/logo.png" />
        <link rel="manifest" href="/manifest.json" />
        <title>Al Ahly SC</title>
      </head>
      <body dir={dir(locale)} className={"antialiased"}>
        {children}
      </body>
    </html>
  );
}
