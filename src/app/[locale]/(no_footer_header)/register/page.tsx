"use client";

import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import translations from "@/lib/locales";
import { usePathname } from "next/navigation";
import { fetchApi } from '@/lib/api';

export default function RegisterForm() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [password, setPassword] = useState("");
  const [passwordConfirm, setPasswordConfirm] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const locale = (() => {
    if (!pathname) return "ar";
    const segments = pathname.split("/").filter(Boolean);
    return segments.length > 0 ? segments[0] : "ar";
  })();
  const t = (key: string) => translations[locale]?.[key] || key;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    if (password !== passwordConfirm) {
      setError(t("register_form_password_mismatch") || "Passwords do not match.");
      setIsLoading(false);
      return;
    }
    try {
      const res = await fetchApi("/api/client/register", {
        method: "POST",
        headers: { "Content-Type": "application/json", "X-localization": locale },
        body: JSON.stringify({
          name: name,
          email,
          phone,
          password,
          password_confirmation: passwordConfirm
        })
      });
      if (res && res.data && res.data.access_token) {
        localStorage.setItem("access_token", res.data.access_token);
        localStorage.setItem("user", JSON.stringify(res.data.user));
        router.push(`/${locale}`);
      } else if (res?.errors) {
        // Show first error message from errors object
        const firstField = Object.keys(res.errors)[0];
        const firstError = Array.isArray(res.errors[firstField]) ? res.errors[firstField][0] : res.errors[firstField];
        setError(firstError || res?.message || t("register_form_error"));
      } else if (res?.message) {
        setError(res.message);
      } else {
        setError(t("register_form_error"));
      }
    } catch (err: any) {
      // Try to extract error message from API response if possible
      if (err?.response) {
        try {
          const data = await err.response.json();
          if (data?.errors) {
            const firstField = Object.keys(data.errors)[0];
            const firstError = Array.isArray(data.errors[firstField]) ? data.errors[firstField][0] : data.errors[firstField];
            setError(firstError || data?.message || t("register_form_error"));
          } else if (data?.message) {
            setError(data.message);
          } else {
            setError(t("register_form_error"));
          }
        } catch {
          setError(t("register_form_error"));
        }
      } else {
        setError(err?.message || t("register_form_error"));
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Language switcher
  const languages = ["ar", "en", "fr"];
  const currentLocale = locale;
  const handleLanguageChange = (lang: string) => {
    if (lang !== currentLocale && pathname) {
      const pathParts = pathname.split("/").filter(Boolean);
      pathParts[0] = lang;
      router.push("/" + pathParts.join("/"));
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#333333] bg-[url('/img/socials-bg.png')] bg-cover p-4">
      <div className="w-full max-w-md bg-[#252323] rounded-lg shadow-lg overflow-hidden border-2 border-[#DFB254]">
        {/* Language Switcher */}
        <div className="flex justify-end p-4">
          <div className="flex gap-2">
            <span className="text-white font-medium">{t("change_language")}:</span>
            {languages.map((lang) => (
              <button
                key={lang}
                onClick={() => handleLanguageChange(lang)}
                className={`px-2 py-1 rounded text-xs font-bold border transition-colors duration-200 ${currentLocale === lang ? "bg-[#DFB254] text-[#252323] border-[#DFB254]" : "bg-transparent text-white border-white hover:bg-[#DFB254] hover:text-[#252323] hover:border-[#DFB254]"}`}
                disabled={currentLocale === lang}
              >
                {lang.toUpperCase()}
              </button>
            ))}
          </div>
        </div>

        {/* Login Header */}
        <div className="bg-[#C60428] py-4 px-6 flex items-center justify-between">
          <h2 className="text-2xl font-bold text-white">
            {t("register_form_title")}
          </h2>
          <Link href={`/${locale}`} className="w-12 h-12 relative block focus:outline-none" title={t("back_to_home")}>
            <Image
              src="/logo.png"
              alt="Al Ahly Logo"
              fill
              className="object-contain"
            />
          </Link>
        </div>

        {/* Register Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-[#80021a] text-white p-3 rounded-md">
              {error}
            </div>
          )}
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-white mb-2">
                {t("register_form_name")}
              </label>
              <input
                id="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full px-4 py-3 bg-[#3a3838] border border-[#DFB254] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#DFB254]"
                required
              />
            </div>
            <div>
              <label htmlFor="email" className="block text-white mb-2">
                {t("register_form_email")}
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 bg-[#3a3838] border border-[#DFB254] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#DFB254]"
                required
              />
            </div>
            <div>
              <label htmlFor="phone" className="block text-white mb-2">
                {t("register_form_phone")}
              </label>
              <input
                id="phone"
                type="tel"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                className="w-full px-4 py-3 bg-[#3a3838] border border-[#DFB254] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#DFB254]"
                required
              />
            </div>
            <div>
              <label htmlFor="password" className="block text-white mb-2">
                {t("register_form_password")}
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 bg-[#3a3838] border border-[#DFB254] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#DFB254]"
                required
              />
            </div>
            <div>
              <label htmlFor="passwordConfirm" className="block text-white mb-2">
                {t("register_form_password_confirm")}
              </label>
              <input
                id="passwordConfirm"
                type="password"
                value={passwordConfirm}
                onChange={(e) => setPasswordConfirm(e.target.value)}
                className="w-full px-4 py-3 bg-[#3a3838] border border-[#DFB254] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#DFB254]"
                required
              />
            </div>
          </div>
          <div className="flex items-center justify-end">
            <button
              type="submit"
              disabled={isLoading}
              className={`px-6 py-3 rounded-md font-medium text-white ${isLoading
                ? "bg-[#a00320] cursor-not-allowed"
                : "bg-[#EC2028] hover:bg-[#d11a2a]"
                } transition-colors duration-300 border-2 border-transparent hover:border-[#DFB254]`}
            >
              {isLoading ? t("register_form_loading") : t("register_form_submit")}
            </button>
          </div>
        </form>

        {/* Footer */}
        <div className="bg-[#3a3838] px-6 py-4 text-center space-y-2">
          <p className="text-white">
            {t("register_form_have_account")} {" "}
            <Link
              href={`/${locale}/login`}
              className="text-[#DFB254] hover:text-[#F8D384] font-medium transition-colors duration-300"
            >
              {t("register_form_login")}
            </Link>
          </p>
          <Link
            href={`/${locale}`}
            className="inline-block mt-2 px-4 py-2 rounded bg-[#DFB254] text-[#252323] font-bold hover:bg-[#F8D384] transition-colors duration-300"
          >
            {t("back_to_home")}
          </Link>
        </div>
      </div>
    </div>
  );
}
