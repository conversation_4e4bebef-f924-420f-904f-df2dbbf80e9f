import type { Metadata } from "next";

const defaultLocale = 'ar';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = resolvedParams?.locale || defaultLocale;
  return {
    title: {
      ar: "إنشاء حساب | النادي الأهلي",
      en: "Register | Al Ahly SC",
      fr: "Inscription | Al Ahly SC"
    }[locale] || "Register | Al Ahly SC",
    description: {
      ar: "صفحة إنشاء حساب جديد لموقع النادي الأهلي",
      en: "Register page for Al Ahly SC website",
      fr: "Page d'inscription du site Al Ahly SC"
    }[locale] || "Register page for Al Ahly SC website",
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL || "https://alahly.com"}/${locale}/register`,
      languages: {
        ar: `${process.env.NEXT_PUBLIC_SITE_URL || "https://alahly.com"}/ar/register`,
        en: `${process.env.NEXT_PUBLIC_SITE_URL || "https://alahly.com"}/en/register`,
        fr: `${process.env.NEXT_PUBLIC_SITE_URL || "https://alahly.com"}/fr/register`,
      },
    },
  };
}

export default function RegisterLayout({ children }: { children: React.ReactNode }) {
  return children;
}