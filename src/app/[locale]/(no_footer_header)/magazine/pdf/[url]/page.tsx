import translations from "@/lib/locales";
import type { Metadata } from "next";
import MagazineFlipBookClient from "@/components/MagazineFlipBookClient";
import { decode as base64Decode } from 'js-base64';

const defaultLocale = 'ar';
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.alahlyegypt.com';

export async function generateMetadata({ params }: { params: any }): Promise<Metadata> {
  // Await params if it's a Promise (for dynamic routes)
  const resolvedParams = typeof params?.then === 'function' ? await params : params;
  const locale = resolvedParams?.locale || defaultLocale;
  const t = (key: string) => translations[locale]?.[key] || key;
  return {
    title: t("alahly_magazine") + " | " + t("site_title"),
    description: t("site_description") || "A website dedicated for Al Ahly SC",
    alternates: {
      canonical: `${siteUrl}/${locale}`,
    },
  };
}

export default async function MagazinePage({ params }: { params: Promise<{ locale: string; url: string }> }) {
  const { locale, url: encodedPdfUrl } = await params;
  const t = (key: string) => translations[locale]?.[key] || key;
  let pdfUrl: string | null = null;
  let errorMsg: string | null = null;

  // Decode the base64-encoded pdfUrl from the route param
  if (encodedPdfUrl) {
    try {
      pdfUrl = base64Decode(decodeURIComponent(encodedPdfUrl));
    } catch (e) {
      errorMsg = t('error_message') || 'حدث خطأ أثناء تحميل المجلة.';
    }
  }

  return (
    <main>
      {/* PDF */}
      {errorMsg ? (
        <div className="text-red-600 text-xl font-bold mt-10">{errorMsg}</div>
      ) : pdfUrl ? (
        <>
          {/* Full width/height flipbook */}
          <MagazineFlipBookClient pdfUrl={pdfUrl} />
        </>
      ) : null}
    </main>
  );
}
