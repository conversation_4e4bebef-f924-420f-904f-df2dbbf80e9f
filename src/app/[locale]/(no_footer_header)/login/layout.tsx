import type { Metadata } from "next";

const defaultLocale = 'ar';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = resolvedParams?.locale || defaultLocale;
  return {
    title: {
      ar: "تسجيل الدخول | النادي الأهلي",
      en: "Login | Al Ahly SC",
      fr: "Connexion | Al Ahly SC"
    }[locale] || "Login | Al Ahly SC",
    description: {
      ar: "صفحة تسجيل الدخول لموقع النادي الأهلي",
      en: "Login page for Al Ahly SC website",
      fr: "Page de connexion du site Al Ahly SC"
    }[locale] || "Login page for Al Ahly SC website",
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL || "https://alahly.com"}/${locale}/login`,
      languages: {
        ar: `${process.env.NEXT_PUBLIC_SITE_URL || "https://alahly.com"}/ar/login`,
        en: `${process.env.NEXT_PUBLIC_SITE_URL || "https://alahly.com"}/en/login`,
        fr: `${process.env.NEXT_PUBLIC_SITE_URL || "https://alahly.com"}/fr/login`,
      },
    },
  };
}

export default function LoginLayout({ children }: { children: React.ReactNode }) {
  return children;
}