"use client";

import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import translations from "@/lib/locales";
import { usePathname } from "next/navigation";
import { fetchApi } from '@/lib/api';

const defaultLocale = 'ar';

export default function LoginForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const locale = (() => {
    if (!pathname) return "ar";
    const segments = pathname.split("/").filter(Boolean);
    return segments.length > 0 ? segments[0] : "ar";
  })();
  const t = (key: string) => translations[locale]?.[key] || key;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    try {
      const res = await fetchApi("/api/client/login", {
        method: "POST",
        headers: { "Content-Type": "application/json", "X-localization": locale },
        body: JSON.stringify({ email, password })
      });
      if (res && res.data && res.data.access_token) {
        // Save token and user info to localStorage
        localStorage.setItem("access_token", res.data.access_token);
        localStorage.setItem("user", JSON.stringify(res.data.user));
        router.push(`/${locale}`);
      } else {
        setError(res?.message || t("login_form_error"));
      }
    } catch (err: any) {
      setError(err?.message || t("login_form_error"));
    } finally {
      setIsLoading(false);
    }
  };

  // Language switcher
  const languages = ["ar", "en", "fr"];
  const currentLocale = locale;
  const handleLanguageChange = (lang: string) => {
    if (lang !== currentLocale && pathname) {
      const pathParts = pathname.split("/").filter(Boolean);
      pathParts[0] = lang;
      router.push("/" + pathParts.join("/"));
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#333333] bg-[url('/img/socials-bg.png')] bg-cover p-4">
      <div className="w-full max-w-md bg-[#252323] rounded-lg shadow-lg overflow-hidden border-2 border-[#DFB254]">
        {/* Language Switcher */}
        <div className="flex justify-end p-4">
          <div className="flex gap-2">
            <span className="text-white font-medium">{t("change_language")}:</span>
            {languages.map((lang) => (
              <button
                key={lang}
                onClick={() => handleLanguageChange(lang)}
                className={`px-2 py-1 rounded text-xs font-bold border transition-colors duration-200 ${currentLocale === lang ? "bg-[#DFB254] text-[#252323] border-[#DFB254]" : "bg-transparent text-white border-white hover:bg-[#DFB254] hover:text-[#252323] hover:border-[#DFB254]"}`}
                disabled={currentLocale === lang}
              >
                {lang.toUpperCase()}
              </button>
            ))}
          </div>
        </div>

        {/* Login Header */}
        <div className="bg-[#C60428] py-4 px-6 flex items-center justify-between">
          <h2 className="text-2xl font-bold text-white">
            {t("login_form_title")}
          </h2>
          <Link href={`/${locale}`} className="w-12 h-12 relative block focus:outline-none" title={t("back_to_home")}>
            <Image
              src="/logo.png"
              alt="Al Ahly Logo"
              fill
              className="object-contain"
            />
          </Link>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-[#80021a] text-white p-3 rounded-md">
              {t("login_form_error")}
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-white mb-2">
                {t("login_form_email")}
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 bg-[#3a3838] border border-[#DFB254] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#DFB254]"
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-white mb-2">
                {t("login_form_password")}
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 bg-[#3a3838] border border-[#DFB254] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#DFB254]"
                required
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Link
              href="#"
              className="text-[#DFB254] hover:text-[#F8D384] transition-colors duration-300"
            >
              {t("login_form_forgot")}
            </Link>

            <button
              type="submit"
              disabled={isLoading}
              className={`px-6 py-3 rounded-md font-medium text-white ${isLoading
                ? "bg-[#a00320] cursor-not-allowed"
                : "bg-[#EC2028] hover:bg-[#d11a2a]"
                } transition-colors duration-300 border-2 border-transparent hover:border-[#DFB254]`}
            >
              {isLoading ? t("loading") : t("login_form_submit")}
            </button>

          </div>
        </form>

        {/* Footer */}
        <div className="bg-[#3a3838] px-6 py-4 text-center space-y-2">
          <p className="text-white">
            {t("login_form_no_account")} {" "}
            <Link
              href={`/${locale}/register`}
              className="text-[#DFB254] hover:text-[#F8D384] font-medium transition-colors duration-300"
            >
              {t("login_form_register")}
            </Link>
          </p>
          <Link
            href={`/${locale}`}
            className="inline-block mt-2 px-4 py-2 rounded bg-[#DFB254] text-[#252323] font-bold hover:bg-[#F8D384] transition-colors duration-300"
          >
            {t("back_to_home")}
          </Link>
        </div>
      </div>
    </div>
  );
}
