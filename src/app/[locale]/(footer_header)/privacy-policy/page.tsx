"use client";
import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { fetchApi } from "@/lib/api";

export default function PrivacyPolicyPage() {
  const pathname = usePathname();
  const locale = pathname ? pathname.split("/")[1] : "ar";
  const [data, setData] = useState<{ title: string; content: string; last_updated: string } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    fetchApi("/api/general/privacy-policy", {
      headers: { "X-localization": locale },
    })
      .then((res) => {
        setData(res.data);
        setLoading(false);
      })
      .catch((err) => {
        setError("Failed to load privacy policy.");
        setLoading(false);
      });
  }, [locale]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[40vh] pt-24 lg:pt-[175px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-red-600 mb-4"></div>
        <div className="text-lg text-white">Loading...</div>
      </div>
    );
  }
  if (error) {
    return <div className="text-center py-10 text-red-500">{error}</div>;
  }
  if (!data) return null;

  return (
    <main className="flex flex-col items-center justify-center min-h-fit pt-32 px-4 text-black bg-white">
      <div className="max-w-3xl w-full bg-white rounded-lg shadow p-6">
        <h1 className="text-3xl font-bold mb-6 text-center text-[#C60428]">{data.title}</h1>
        <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: data.content }} />
        <div className="mt-8 text-xs text-gray-400 text-right">
          Last updated: {data.last_updated}
        </div>
      </div>
    </main>
  );
}
