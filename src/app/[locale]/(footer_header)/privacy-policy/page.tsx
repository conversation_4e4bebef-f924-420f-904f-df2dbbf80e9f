import { getCachedStaticData } from '@/lib/cache-helpers';

// ISR configuration - revalidate every 24 hours for static content
export const revalidate = 86400;

// Use cached static data fetcher
const getStaticData = getCachedStaticData;

export default async function PrivacyPolicyPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  const response = await getStaticData('/api/general/privacy-policy', locale, ['privacy-policy']);

  if (!response?.data) {
    return <div className="text-center py-10 text-red-500">Failed to load privacy policy.</div>;
  }

  const data = response.data;

  return (
    <main className="flex flex-col items-center justify-center min-h-fit pt-32 px-4 text-black bg-white">
      <div className="max-w-3xl w-full bg-white rounded-lg shadow p-6">
        <h1 className="text-3xl font-bold mb-6 text-center text-[#C60428]">{data.title}</h1>
        <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: data.content }} />
        <div className="mt-8 text-xs text-gray-400 text-right">
          Last updated: {data.last_updated}
        </div>
      </div>
    </main>
  );
}
