"use client";

import React, { useEffect, useState } from "react";
import translations from "@/lib/locales";
import Image from "next/image";
import { fetchApi } from "@/lib/api";

export default function MagazineListPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = React.use(params);
  const t = (key: string) => translations[locale]?.[key] || key;
  const [magazines, setMagazines] = useState<any[]>([]);
  const [magazineLoading, setMagazineLoading] = useState(true);
  const [magazineError, setMagazineError] = useState<string | null>(null);
  const [activeIndex, setActiveIndex] = useState(0);


  useEffect(() => {
    async function fetchMagazines() {
      setMagazineLoading(true);
      setMagazineError(null);
      try {
        const res = await fetchApi("/api/magazine/list", { headers: { "X-localization": locale } });
        if (res.status && Array.isArray(res.magazines)) {
          setMagazines(res.magazines);
        } else {
          setMagazineError(t("no_magazines") || "No magazines found");
        }
      } catch (err) {
        setMagazineError(t("no_magazines") || "No magazines found");
      } finally {
        setMagazineLoading(false);
      }
    }
    fetchMagazines();
  }, [locale]);

  return (
    <main className="flex flex-col bg-[#f6f6f6] min-h-fit pt-24 pb-12 lg:pt-[125px]">
      {/* Header with stadium image and overlay title */}
      <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
        <Image
          src="/img/magazine_bg.png"
          alt={t("alahly_magazine") || "مجلة الأهلي"}
          fill
          className="object-cover w-full h-full"
          priority
        />

        <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
            {(t("alahly_magazine") || "مجلة الأهلي").split('\n').map((line: string, idx: number, arr: string[]) => (
              <React.Fragment key={idx}>
                {line}
                {idx !== arr.length - 1 && <br />}
              </React.Fragment>
            ))}
          </h2>
        </div>
      </div>
      <section className="max-w-6xl mx-auto w-full py-12 px-4">
        {/* Magazine List */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-[#C60428] mb-6">{t('magazine_list') || 'أعداد مجلة'}</h2>
          {magazineLoading ? (
            <div className="text-center text-[#C60428] py-10 text-lg font-bold">{t("loading")}</div>
          ) : magazineError ? (
            <div className="text-center text-red-600 py-10 text-lg font-bold">{magazineError}</div>
          ) : (
            <div className="flex flex-col gap-6">
              {magazines.map((magazine, idx) => (
                <MagazineAccordionSection
                  key={magazine.id}
                  magazine={magazine}
                  t={t}
                  locale={locale}
                  open={idx === activeIndex}
                  onToggle={() => setActiveIndex(idx === activeIndex ? -1 : idx)}
                />
              ))}
            </div>
          )}
        </div>
        {/* End Magazine List */}
      </section>
    </main>
  );
}

function MagazineAccordionSection({ magazine, t, locale, open, onToggle }: { magazine: any, t: (key: string) => string, locale: string, open: boolean, onToggle: () => void }) {
  return (
    <div className="bg-white rounded-xl shadow overflow-hidden">
      <button
        className="w-full flex items-center justify-between p-6 focus:outline-none hover:bg-gray-50 transition-colors"
        onClick={onToggle}
        aria-expanded={open}
      >
        <div className="flex items-center gap-4">
          <div className="w-20 h-20 relative rounded-lg overflow-hidden flex-shrink-0 bg-[#f6f6f6] flex items-center justify-center">
            <Image src="/img/default_bg.png" alt={magazine.title} fill className="object-cover w-full h-full" />
          </div>
          <h3 className="text-xl font-bold text-[#C60428]">{magazine.title}</h3>
        </div>
        <span className={`transition-transform duration-300 text-2xl ${open ? 'rotate-180' : ''}`}>⌄</span>
      </button>
      {open && (
        <div className="border-t px-6 pb-6 pt-2 animate-fade-in">
          <div className="mb-4 text-[#222] text-base leading-relaxed whitespace-pre-line">{magazine.content}</div>
          <div className="flex items-center gap-4">
            <span className="text-gray-500 text-sm">{magazine.version}</span>
            <a
              href={`/${locale}/magazine/pdf/${encodeURIComponent(btoa(magazine.pdf_url))}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block px-6 py-2 bg-[#C60428] text-white rounded-lg font-bold hover:bg-[#a3001e] transition-colors"
            >
              {t('download_pdf') || 'تحميل المجلة PDF'}
            </a>
          </div>
        </div>
      )}
    </div>
  );
}
