import Image from "next/image";
import translations from "@/lib/locales";
import { fetchApi } from "@/lib/api";

export type Trophy = {
  id: number;
  title: string;
  count: number;
  image?: string | null;
  sport_id?: number;
};

export default async function AchievementsPage({ params }: { params: Promise<{ locale: string; }> }) {
  const { locale } = await params;
  const t = (key: string) => translations[locale]?.[key] || key;

  const res = await fetchApi(`/api/widgets/sports/kr-alkdm`, {
    headers: { 'X-localization': locale },
  });
  const data = res?.data || {};
  const sport = data.sport || {};
  const trophies = data.trophies || [];

  return (
    <main className="flex flex-col gap-3 min-h-fit bg-black pt-12 pb-24 lg:pt-[125px]">
      {/* Cover Image Section */}
      <div className="relative w-full h-[200px] md:h-[300px] lg:h-[400px] overflow-hidden">
        <Image
          src={"/img/trophies_bg.png"}
          alt="trophie cover"
          fill
          className="object-cover object-center"
          priority />
        <div className="absolute inset-0 bg-black/40" />
        <div className="absolute inset-0 flex flex-col items-start justify-center z-10 pl-8 md:pl-16 ltr:ml-28 rtl:mr-28">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-2 drop-shadow-lg">{sport.title || t("trophie_title") || "رياضة كرة القدم"}</h1>
          <p className="text-lg md:text-2xl text-white/80 drop-shadow-lg">{t("trophies") || "كل ما يخص رياضة كرة القدم في النادي الأهلي"}</p>
        </div>
      </div>

      {/* Trophies Section */}
      <section className="mt-12">
        <div className="w-full p-4 sm:p-6 md:p-8 text-white">
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-6 gap-8 justify-items-center">
            {trophies.length > 0 ? trophies.map((trophy: any, i: number) => (
              <div
                key={trophy.id || i}
                className="flex flex-col rounded-sm w-[150px] h-[220px] sm:w-[180px] sm:h-[250px] md:w-[200px] md:h-[280px] lg:w-[250px] lg:h-[325px] overflow-clip drop-shadow-md bg-[#181818] border border-[#DFB254]"
              >
                <div className="bg-[#EC2028] min-h-[120px] sm:min-h-[150px] md:min-h-[170px] lg:min-h-[205px] w-full flex justify-center items-center">
                  <Image
                    src={trophy.image || "/trophies/1.png"}
                    alt={trophy.title}
                    width={100}
                    height={100}
                    className="w-[60px] h-[60px] sm:w-[80px] sm:h-[80px] md:w-[100px] md:h-[100px] lg:w-auto lg:h-auto" />
                </div>
                <div className="bg-[#DB052C] min-h-[100px] sm:min-h-[100px] md:min-h-[110px] lg:min-h-[120px] w-full flex flex-col justify-center items-center border-dashed border-t border-white/50 p-2">
                  <p className="text-3xl sm:text-4xl md:text-5xl font-semibold">
                    {trophy.count}
                  </p>
                  <p className="text-xs sm:text-sm md:text-base lg:text-lg font-semibold text-center truncate w-full" title={trophy.title}>
                    {trophy.title}
                  </p>
                </div>
              </div>
            )) : (
              <div className="text-center text-gray-400 w-full col-span-5">{t("no_trophies") || "لا توجد بطولات"}</div>
            )}
          </div>
        </div>
      </section>

    </main>
  );
}
