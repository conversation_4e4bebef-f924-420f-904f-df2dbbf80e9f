import Image from "next/image";
import translations from "@/lib/locales";
import { fetchApi } from "@/lib/api";
import SportsResultsSection from "@/components/sports/SportsResultsSection";
import SportsPlayersSection from "@/components/sports/SportsPlayersSection";
import SportsVideoSection from "@/components/sports/SportsVideoSection";
import SportsNewsSection from "@/components/sports/SportsNewsSection";
import SportsStaffSection from "@/components/sports/SportsStaffSection";
import SportsTrophiesSection from "@/components/sports/SportsTrophiesSection";
import GlobalCover from "@/components/GlobalCover";

export type Trophy = {
  id: number;
  title: string;
  count: number;
  image?: string | null;
  sport_id?: number;
};

export default async function SportsPage({ params }: { params: Promise<{ locale: string; }> }) {
  const { locale } = await params;
  const t = (key: string) => translations[locale]?.[key] || key;

  let res;
  try {
    res = await fetchApi(`/api/widgets/sports/kr-alkdm`, {
      headers: { 'X-localization': locale },
    });
  } catch {
    return (
      <main className="flex flex-col min-h-fit pt-12 lg:pt-[125px] pb-24 items-center justify-center">
        <div className="text-red-600 text-xl font-bold mt-20">{t('error_message') || 'حدث خطأ أثناء تحميل الصفحة.'}</div>
      </main>
    );
  }
  const data = res?.data || {};
  if (!data.sport) {
    return (
      <main className="flex flex-col min-h-fit pt-12 lg:pt-[125px] pb-24 items-center justify-center">
        <div className="text-red-600 text-xl font-bold mt-20">{t('error_message') || 'حدث خطأ أثناء تحميل الصفحة.'}</div>
      </main>
    );
  }
  const sport = data.sport || {};
  const news = data.news || [];
  const matches = data.matches || [];
  const players = data.players || [];
  const trophies = data.trophies || [];
  const staff = data.staff || [];
  const videos = data.videos || [];

  return (
    <main className="flex flex-col gap-3 min-h-fit bg-black pt-12 pb-12 lg:pt-[125px]">
      {/* Cover Image Section */}
      <GlobalCover image={sport.cover} title={sport.title} fallbackImage="/img/default_bg.png" fallbackTitle={t("sports_title")} />

      {/* Latest Results */}
      <SportsResultsSection matches={matches} t={t} />

      {/* Related News Section */}
      <SportsNewsSection news={news} locale={locale} t={t} />

      {/* Players Section */}
      <SportsPlayersSection players={players} t={t} />

      {/* Video Highlight Section */}
      <SportsVideoSection video={videos[0]} locale={locale} />

      {/* Technical Staff Section */}
      <SportsStaffSection staff={staff} t={t} />

      {/* Trophies Section */}
      <SportsTrophiesSection trophies={trophies} locale={locale} />

    </main>
  );
}
