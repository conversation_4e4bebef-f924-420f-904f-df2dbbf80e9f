"use client";
import Image from "next/image";
import translations from "@/lib/locales";
import { fetchApi } from "@/lib/api";
import { useEffect, useState } from "react";
import React from "react";

// Helper to get months for a champion
const getMonths = (championMatches: any, locale: string) => {
  if (!championMatches) return [];
  // Use all months present in the data, in the order they appear
  return Object.keys(championMatches)
    .filter(key => championMatches[key] && championMatches[key].length > 0)
    .map(key => ({ key, display: key }));
};

export default function MatchSchedulePage({ params }: { params: Promise<{ locale: string; }> }) {
  const { locale } = React.use(params);
  const t = (key: string) => translations[locale]?.[key] || key;

  const [data, setData] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Track selected month per competition
  const [selectedMonths, setSelectedMonths] = useState<Record<string, string>>({});

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetchApi(`/api/results/matches-schedule`, {
          headers: { 'X-localization': locale },
        });
        setData(res || {});
      } catch (err: any) {
        setError(t('error_loading_data') || 'حدث خطأ أثناء تحميل البيانات');
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [locale]);

  const matchesByCompetition = data.matches || {};

  // Set default selected month for each competition
  useEffect(() => {
    if (!matchesByCompetition) return;
    const newSelected: Record<string, string> = {};
    Object.entries(matchesByCompetition).forEach(([competition, monthsObj]) => {
      const months = getMonths(monthsObj, locale);
      if (months.length > 0) {
        // Try to select the first month with matches (descending by date)
        newSelected[competition] = months[0].key;
      }
    });
    setSelectedMonths(newSelected);
  }, [data, locale]);

  return (
    <main className="flex flex-col min-h-fit bg-[#f6f6f6] pt-12 pb-12 lg:pt-[125px]">
      {/* Header */}
      <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
        <Image
          src="/img/results_head.jpg"
          alt={t("match_schedule")}
          fill
          className="object-cover w-full h-full"
          priority
        />
        {/* Display season name from API at the top-end (top right in LTR, top left in RTL) */}
        {(data.season || data.season_name) && (
          <div className="absolute top-14 end-8 px-4 py-2 rounded-lg bg-[#C60428] text-white text-lg md:text-xl font-bold shadow z-20" style={{ textShadow: '0 1px 5px #dba527' }}>
            {data.season || data.season_name}
          </div>
        )}
        <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
            {t("match_schedule")}
          </h2>
        </div>
      </div>

      {/* Error Handler */}
      {error && (
        <div className="mx-4 my-8 p-4 bg-red-100 border border-red-400 text-red-700 rounded text-center font-bold">
          {error}
        </div>
      )}
      {/* Global Loading Handler */}
      {loading && !error && (
        <div className="flex justify-center items-center min-h-[200px]">
          <span className="text-[#C60428] text-xl font-bold animate-pulse">{t("loading")}</span>
        </div>
      )}
      {/* Matches Section: Render for each competition */}
      {!loading && !error && Object.entries(matchesByCompetition).map(([competition, monthsObj]: [string, any]) => {
        const months = getMonths(monthsObj, locale);
        const selectedMonth = selectedMonths[competition] || (months[0] && months[0].key);
        // Only render if there are matches in any month
        const hasMatches = months.some(m => (monthsObj[m.key] || []).length > 0);
        if (!hasMatches) return null;
        return (
          <div key={competition} className="w-full bg-white rounded-xl shadow mt-8">
            <div className="flex items-center gap-4 px-6 pt-6">
              <span className="bg-[#C60428] text-white rounded px-4 py-2 text-lg font-bold">{competition}</span>
            </div>
            {/* Month Tabs */}
            <div className="flex flex-wrap justify-center border-b border-gray-200 mt-2">
              {months.map((month, idx) => (
                <div
                  key={month.key}
                  className={`px-6 py-3 cursor-pointer font-bold text-lg border-b-4 ${selectedMonth === month.key ? 'border-[#C60428] text-[#C60428] bg-[#f6f6f6]' : 'border-transparent text-gray-700'}`}
                  onClick={() => setSelectedMonths(s => ({ ...s, [competition]: month.key }))}
                >
                  {month.display}
                </div>
              ))}
            </div>
            {/* Matches for selected month */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-6">
              {(selectedMonth && (monthsObj[selectedMonth] || []).length > 0) ? (
                (monthsObj[selectedMonth] || []).map((match: any, idx: number) => (
                  <div key={idx} className="grid grid-cols-3 items-center bg-white shadow p-4 gap-4 w-full">
                    {/* Home Club */}
                    <div className="flex flex-col items-center gap-2 min-w-[90px] max-w-[110px] mx-auto">
                      {match.home_logo && (
                        <Image src={match.home_logo} alt={match.home_team} width={48} height={48} className="object-contain h-12 w-12" />
                      )}
                      <span className="font-bold text-sm text-gray-800 text-center truncate w-full" title={match.home_team}>{match.home_team}</span>
                    </div>
                    {/* Match Info */}
                    <div className="flex flex-col items-center bg-[#C60428] py-2 px-1 w-full">
                      <span className="text-2xl font-bold text-white">{match.time?.slice(0, 5)}</span>
                      <span className="text-xl text-white mt-1">{match.date} </span>
                      <span className="text-xs text-white mt-1">{match.competition}</span>
                    </div>
                    {/* Away Club */}
                    <div className="flex flex-col items-center gap-2 min-w-[90px] max-w-[110px] mx-auto">
                      {match.away_logo && (
                        <Image src={match.away_logo} alt={match.away_team} width={48} height={48} className="object-contain h-12 w-12" />
                      )}
                      <span className="font-bold text-sm text-gray-800 text-center truncate w-full" title={match.away_team}>{match.away_team}</span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-400 py-8">{t("no_matches_this_month") || "لا توجد مباريات في هذا الشهر"}</div>
              )}
            </div>
          </div>
        );
      })}
    </main>
  );
}
