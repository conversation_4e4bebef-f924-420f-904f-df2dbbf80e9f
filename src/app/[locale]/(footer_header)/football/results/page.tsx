"use client";
import Image from "next/image";
import translations from "@/lib/locales";
import { fetchApi } from "@/lib/api";
import { useEffect, useState } from "react";
import React from "react";

const MONTHS: Record<string, string[]> = {
  ar: [
    "يونيو", "مايو", "أبريل", "مارس", "فبراير", "يناير", "ديسمبر", "نوفمبر", "أكتوبر", "سبتمبر", "أغسطس"
  ],
  en: [
    "June", "May", "April", "March", "February", "January", "December", "November", "October", "September", "August"
  ],
  fr: [
    "Juin", "Mai", "Avril", "Mars", "Février", "Janvier", "Décembre", "Novembre", "Octobre", "Septembre", "Août"
  ]
};

const STATUS_COLORS: Record<string, string> = {
  "صعود": "bg-green-500",
  "هبوط": "bg-red-500",
  "كأس كونفدرالية": "bg-blue-500",
  "تأهل إفريقي": "bg-cyan-500",
  "عادي": "bg-gray-300"
};

// Helper to get months for a champion
const getMonths = (championMatches: any, locale: string) => {
  if (!championMatches) return [];
  if (locale === 'fr') {
    return Object.keys(championMatches)
      .filter(key => championMatches[key] && championMatches[key].length > 0)
      .map(key => ({ key, display: key }));
  } else {
    return (MONTHS[locale] || MONTHS['ar'])
      .map(m => m.trim())
      .filter(m => championMatches[m] && championMatches[m].length > 0)
      .map(m => ({ key: m, display: m }));
  }
};

export default function ResultsPage({ params }: { params: Promise<{ locale: string; }> }) {
  const { locale } = React.use(params);
  const t = (key: string) => translations[locale]?.[key] || key;

  const [data, setData] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Track selected month per champion
  const [selectedMonths, setSelectedMonths] = useState<Record<string, string>>({});

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetchApi(`/api/results/seasons`, {
          headers: { 'X-localization': locale },
        });
        setData(res || {});
      } catch (err: any) {
        setError(t('error_loading_data') || 'حدث خطأ أثناء تحميل البيانات');
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [locale]);

  const matchesByChampion = data.matches || {};

  // Convert new standings array to grouped object for rendering
  const standingsByChampion = React.useMemo(() => {
    if (!Array.isArray(data.standings)) return {};
    const result: Record<string, Record<string, Record<string, any>>> = {};
    data.standings.forEach((champion: any) => {
      const championTitle = champion.title;
      if (!result[championTitle]) result[championTitle] = {};
      (champion.stages || []).forEach((stage: any) => {
        const stageTitle = stage.title;
        if (!result[championTitle][stageTitle]) result[championTitle][stageTitle] = {};
        (stage.groups || []).forEach((group: any) => {
          const groupTitle = group.title || t('group');
          result[championTitle][stageTitle][groupTitle] = group;
        });
      });
    });
    return result;
  }, [data.standings, t]);

  // Set default selected month for each champion
  useEffect(() => {
    if (!matchesByChampion) return;
    const newSelected: Record<string, string> = {};
    Object.entries(matchesByChampion).forEach(([champion, monthsObj]) => {
      const months = getMonths(monthsObj, locale);
      if (months.length > 0) {
        // Try to select current month if available
        const now = new Date();
        let nowMonth = now.toLocaleString(locale === 'fr' ? 'fr-FR' : locale === 'en' ? 'en-US' : 'ar-EG', { month: 'long' })
          .replace(/\u200f/g, '').trim();
        if (locale === 'fr') {
          nowMonth = nowMonth.normalize('NFD').replace(/\p{Diacritic}/gu, '').toLowerCase();
          const found = months.find(m => m.key.normalize('NFD').replace(/\p{Diacritic}/gu, '') === nowMonth);
          newSelected[champion] = found ? found.key : months[0].key;
        } else {
          if (locale === 'en') nowMonth = nowMonth.charAt(0).toUpperCase() + nowMonth.slice(1);
          const found = months.find(m => m.key.includes(nowMonth) || nowMonth.includes(m.key));
          newSelected[champion] = found ? found.key : months[0].key;
        }
      }
    });
    setSelectedMonths(newSelected);
  }, [data, locale]);

  return (
    <main className="flex flex-col min-h-fit bg-[#f6f6f6] pt-12 pb-12 lg:pt-[125px]">
      {/* Header */}
      <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
        <Image
          src="/img/results_head.jpg"
          alt={t("results")}
          fill
          className="object-cover w-full h-full"
          priority
        />
        {/* Display season name from API at the top-end (top right in LTR, top left in RTL) */}
        {(data.season || data.season_name) && (
          <div className="absolute top-14 end-8 px-4 py-2 rounded-lg bg-[#C60428] text-white text-lg md:text-xl font-bold shadow z-20" style={{ textShadow: '0 1px 5px #dba527' }}>
            {data.season || data.season_name}
          </div>
        )}
        <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-24 ms-24" style={{ textShadow: '0 1px 5px #dba527' }}>
            {t("نتائج المباريات")}
          </h2>
        </div>
      </div>

      {/* Error Handler */}
      {error && (
        <div className="mx-4 my-8 p-4 bg-red-100 border border-red-400 text-red-700 rounded text-center font-bold">
          {error}
        </div>
      )}
      {/* Global Loading Handler */}
      {loading && !error && (
        <div className="flex justify-center items-center min-h-[200px]">
          <span className="text-[#C60428] text-xl font-bold animate-pulse">{t("loading")}</span>
        </div>
      )}
      {/* Matches Section: Render for each champion */}
      {!loading && !error && Object.entries(matchesByChampion).map(([champion, monthsObj]: [string, any]) => {
        const months = getMonths(monthsObj, locale);
        const selectedMonth = selectedMonths[champion] || (months[0] && months[0].key);
        return (
          <div key={champion} className="w-full bg-white rounded-xl shadow mt-8">
            <div className="flex items-center gap-4 px-6 pt-6">
              <span className="bg-[#C60428] text-white rounded px-4 py-2 text-lg font-bold">{champion}</span>
            </div>
            {/* Month Tabs */}
            <div className="flex flex-wrap justify-center border-b border-gray-200 mt-2">
              {months.map((month, idx) => (
                <div
                  key={month.key}
                  className={`px-6 py-3 cursor-pointer font-bold text-lg border-b-4 ${selectedMonth === month.key ? 'border-[#C60428] text-[#C60428] bg-[#f6f6f6]' : 'border-transparent text-gray-700'}`}
                  onClick={() => setSelectedMonths(s => ({ ...s, [champion]: month.key }))}
                >
                  {month.display}
                </div>
              ))}
            </div>
            {/* Matches for selected month */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-6">
              {loading ? (
                <div className="text-center text-[#C60428] py-8">{t("loading")}</div>
              ) : error ? (
                <div className="text-center text-red-500 py-8">{error}</div>
              ) : (selectedMonth && (monthsObj[selectedMonth] || []).map((match: any, idx: number) => (
                <div key={idx} className="flex flex-col md:flex-row items-center justify-between bg-white rounded-lg shadow p-4 gap-4">
                  <div className="flex flex-col items-center gap-2 w-1/3">
                    {match.home_logo && <Image src={match.home_logo} alt={match.home_team} width={48} height={48} className="object-contain h-12 w-12" />}
                    <span className="font-bold text-sm text-gray-800">{match.home_team}</span>
                  </div>
                  <div className="flex flex-col items-center w-1/3">
                    <span className="text-2xl font-bold text-[#C60428]">{match.score || '-'}</span>
                    <span className="text-xs text-gray-500 mt-1">{match.date} | {match.time?.slice(0, 5)}</span>
                    <span className="text-xs text-gray-400 mt-1">{match.competition}</span>
                  </div>
                  <div className="flex flex-col items-center gap-2 w-1/3">
                    {match.away_logo && <Image src={match.away_logo} alt={match.away_team} width={48} height={48} className="object-contain h-12 w-12" />}
                    <span className="font-bold text-sm text-gray-800">{match.away_team}</span>
                  </div>
                </div>
              )))}
              {(!loading && !error && selectedMonth && (monthsObj[selectedMonth] || []).length === 0) && (
                <div className="text-center text-gray-400 py-8">{t("no_matches_this_month") || "لا توجد مباريات في هذا الشهر"}</div>
              )}
            </div>
          </div>
        );
      })}

      {/* Standings Section: Render for each champion, stage, group */}
      {!loading && !error && Object.entries(standingsByChampion).map(([champion, stagesObj]: [string, any]) => (
        Object.entries(stagesObj).map(([stage, groupsObj]: [string, any]) => (
          Object.entries(groupsObj).map(([group, table]: [string, any]) => (
            <div key={champion + stage + group} className="mb-12 mt-12 mx-4">
              <div className="flex items-center gap-4 mb-2">
                <span className="bg-[#C60428] text-white rounded px-4 py-2 text-lg font-bold">{champion}</span>
                <span className="bg-gray-200 text-[#C60428] rounded px-4 py-2 text-base font-bold">{stage}</span>
                <span className="bg-gray-100 text-gray-700 rounded px-4 py-2 text-base font-bold">{group}</span>
              </div>
              {/* Legend */}
              <div className="overflow-x-auto bg-white rounded-xl shadow">
                {loading ? (
                  <div className="text-center text-[#C60428] py-8">{t("loading")}</div>
                ) : error ? (
                  <div className="text-center text-red-500 py-8">{error}</div>
                ) : (
                  <table className="min-w-full text-center border-separate border-spacing-y-2">
                    <thead>
                      <tr className="bg-[#f3f3f3] text-[#C60428]">
                        <th className="py-2 px-2">{t("table_rank")}</th>
                        <th className="py-2 px-2 text-start">{t("table_team")}</th>
                        <th className="py-2 px-2">{t("table_played")}</th>
                        <th className="py-2 px-2">{t("table_wins")}</th>
                        <th className="py-2 px-2">{t("table_draws")}</th>
                        <th className="py-2 px-2">{t("table_losses")}</th>
                        <th className="py-2 px-2">{t("table_goals_for")}</th>
                        <th className="py-2 px-2">{t("table_goals_against")}</th>
                        <th className="py-2 px-2">{t("table_goal_diff")}</th>
                        <th className="py-2 px-2">{t("table_points")}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {table.items.map((row: any, i: number) => (
                        <tr key={row.team + i} className="bg-white">
                          <td className="py-2 px-2">
                            <span className={`inline-block w-2 h-8 rounded-full align-middle ms-2`}></span>
                            {i + 1}
                          </td>
                          <td className="py-2 px-2 flex items-center gap-2 justify-start">
                            {row.logo && <Image src={row.logo} alt={row.team} width={28} height={28} className="object-contain h-7 w-7" />}
                            <span>{row.team}</span>
                          </td>
                          <td className="py-2 px-2">{row.played}</td>
                          <td className="py-2 px-2">{row.wins}</td>
                          <td className="py-2 px-2">{row.draws}</td>
                          <td className="py-2 px-2">{row.losses}</td>
                          <td className="py-2 px-2">{row.goals_for}</td>
                          <td className="py-2 px-2">{row.goals_against}</td>
                          <td className="py-2 px-2">{row.goal_diff}</td>
                          <td className="py-2 px-2 font-bold text-[#C60428]">{row.points}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          ))
        ))
      ))}
    </main>
  );
}
