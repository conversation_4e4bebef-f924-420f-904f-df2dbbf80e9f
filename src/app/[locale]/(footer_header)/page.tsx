"use client";

import AdsSection from "@/components/home/<USER>";
import HeroSection from "@/components/home/<USER>";
import NewsSection from "@/components/home/<USER>";
import SocialsSection from "@/components/home/<USER>";
import TeamSection from "@/components/home/<USER>";
import TrophiesSection from "@/components/home/<USER>";
import VideosSection from "@/components/home/<USER>";
import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { fetchApi } from "@/lib/api";
import { useGlobalData } from "@/context/GlobalDataContext";

export default function Home() {
  const [homeData, setHomeData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [showBanner, setShowBanner] = useState(false);
  const { top_banner } = useGlobalData();
  const pathname = usePathname();
  const locale = pathname ? pathname.split("/")[1] : "ar";

  useEffect(() => {
    setLoading(true);
    fetchApi("/api/widgets/home", {
      headers: {
        "X-localization": locale,
      },
    })
      .then((data) => {
        setHomeData(data.data);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message);
        setLoading(false);
      });
  }, [locale]);

  useEffect(() => {
    // Check if top banner is shown (sessionStorage logic must match Navbar)
    const isHome = pathname === `/${locale}` || pathname === `/${locale}/`;
    const bannerClosed = typeof window !== 'undefined' && sessionStorage.getItem('top_banner_closed') === '1';
    // You may want to fetch the banner data here or pass as prop/context
    // For now, just check if AdsSection banner prop is present
    setShowBanner(!!top_banner && isHome && !bannerClosed);
  }, [pathname, locale, homeData]);

  // Listen for banner close event from Navbar and update padding immediately
  useEffect(() => {
    function checkBanner() {
      const isHome = pathname === `/${locale}` || pathname === `/${locale}/`;
      const bannerClosed = typeof window !== 'undefined' && sessionStorage.getItem('top_banner_closed') === '1';
      setShowBanner(!!top_banner && isHome && !bannerClosed);
    }
    function handleStorage(e: StorageEvent) {
      if (e.key === 'top_banner_closed') {
        checkBanner();
      }
    }
    window.addEventListener('storage', handleStorage);
    window.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') checkBanner();
    });
    checkBanner();
    // Listen for custom event from Navbar close button for instant update
    function handleBannerClosed() {
      checkBanner();
    }
    window.addEventListener('top_banner_closed', handleBannerClosed);
    return () => {
      window.removeEventListener('storage', handleStorage);
      window.removeEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') checkBanner();
      });
      window.removeEventListener('top_banner_closed', handleBannerClosed);
    };
  }, [pathname, locale, homeData]);

  if (loading)
    return (
      <div className="flex flex-col items-center justify-center min-h-[40vh] pt-24 lg:pt-[175px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-red-600 mb-4"></div>
        <div className="text-lg text-white">Loading...</div>
      </div>
    );

  if (error) return <div className="text-center py-10 text-red-500">{error}</div>;

  return (
    <main className="flex flex-col gap-3 min-h-fit text-white">
      <div className={`flex flex-col gap-3 row-start-2 items-center sm:items-start min-h-fit px-2 text-white transition-all duration-300 ${showBanner ? 'pt-32 lg:pt-106' : 'pt-12 lg:pt-40'}`}>
        {homeData?.news?.length > 0 && (
          <HeroSection
            news={homeData.news.map((item: any) => ({
              src: item.image_full,
              slug: item.slug,
              title: item.title,
              intro: item.intro,
            }))}
            matches={homeData.upcoming_matches || []}
          />
        )}
        <AdsSection banner={homeData?.banner} />
        {homeData?.first_category_news?.length > 0 && (
          <NewsSection
            news={homeData.first_category_news.map((item: any) => ({
              src: item.image_full,
              title: item.title,
              intro: item.intro,
              slug: item.slug,
              published_at: item.published_at,
            }))}
            categories={homeData.categories || []}
            leagueTable={homeData.league_table || []}
            leaugeTitle={homeData.league_title || ''}
          />
        )}
        {homeData?.videos?.length > 0 && <VideosSection videos={homeData.videos} />}
        {Array.isArray(homeData?.players) && homeData.players.length > 0 && <TeamSection players={homeData.players} />}
        {homeData?.trophies?.length > 0 && (
          <TrophiesSection trophiesData={{
            total_trophies: homeData.total_trophies ?? '',
            trophies: homeData.trophies ?? []
          }} />
        )}
        {(homeData?.socials?.length > 0 || homeData?.social_facebook || homeData?.social_instagram || homeData?.social_x || homeData?.social_tiktok || homeData?.social_youtube || homeData?.social_snapchat) && (
          <SocialsSection 
            socials={homeData.socials} 
            socialLinks={{
              social_facebook: homeData.social_facebook,
              social_instagram: homeData.social_instagram,
              social_x: homeData.social_x,
              social_tiktok: homeData.social_tiktok,
              social_youtube: homeData.social_youtube,
              social_snapchat: homeData.social_snapchat,
            }}
          />
        )}
      </div>
    </main>

  );
}
