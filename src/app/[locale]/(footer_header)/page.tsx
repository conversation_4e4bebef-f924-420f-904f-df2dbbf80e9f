import { Metadata } from 'next';
import { getCachedHomeData } from '@/lib/cache-helpers';
import translations from '@/lib/locales';
import AdsSection from "@/components/home/<USER>";
import HeroSection from "@/components/home/<USER>";
import NewsSection from "@/components/home/<USER>";
import SocialsSection from "@/components/home/<USER>";
import TeamSection from "@/components/home/<USER>";
import TrophiesSection from "@/components/home/<USER>";
import VideosSection from "@/components/home/<USER>";
import HomePageWrapper from "@/components/home/<USER>";

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.alahlyegypt.com';

// ISR configuration - revalidate every 15 minutes
export const revalidate = 900;

// Use the cached home data fetcher
const getHomeData = getCachedHomeData;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = (key: string) => translations[locale]?.[key] || key;

  return {
    title: t('site_title'),
    description: t('site_description'),
    alternates: {
      canonical: `${siteUrl}/${locale}`,
    },
    openGraph: {
      title: t('site_title'),
      description: t('site_description'),
      url: `${siteUrl}/${locale}`,
    },
    twitter: {
      card: 'summary_large_image',
      title: t('site_title'),
      description: t('site_description'),
    },
  };
}

export default async function Home({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  const homeDataResponse = await getHomeData('/api/widgets/home', locale, ['home']);

  if (!homeDataResponse?.data) {
    return <div className="text-center py-10 text-red-500">Failed to load home data</div>;
  }

  const homeData = homeDataResponse.data;

  return (
    <HomePageWrapper locale={locale}>
        {homeData?.news?.length > 0 && (
          <HeroSection
            news={homeData.news.map((item: any) => ({
              src: item.image_full,
              slug: item.slug,
              title: item.title,
              intro: item.intro,
            }))}
            matches={homeData.upcoming_matches || []}
          />
        )}
        <AdsSection banner={homeData?.banner} />
        {homeData?.first_category_news?.length > 0 && (
          <NewsSection
            news={homeData.first_category_news.map((item: any) => ({
              src: item.image_full,
              title: item.title,
              intro: item.intro,
              slug: item.slug,
              published_at: item.published_at,
            }))}
            categories={homeData.categories || []}
            leagueTable={homeData.league_table || []}
            leaugeTitle={homeData.league_title || ''}
          />
        )}
        {homeData?.videos?.length > 0 && <VideosSection videos={homeData.videos} />}
        {Array.isArray(homeData?.players) && homeData.players.length > 0 && <TeamSection players={homeData.players} />}
        {homeData?.trophies?.length > 0 && (
          <TrophiesSection trophiesData={{
            total_trophies: homeData.total_trophies ?? '',
            trophies: homeData.trophies ?? []
          }} />
        )}
        {(homeData?.socials?.length > 0 || homeData?.social_facebook || homeData?.social_instagram || homeData?.social_x || homeData?.social_tiktok || homeData?.social_youtube || homeData?.social_snapchat) && (
          <SocialsSection 
            socials={homeData.socials} 
            socialLinks={{
              social_facebook: homeData.social_facebook,
              social_instagram: homeData.social_instagram,
              social_x: homeData.social_x,
              social_tiktok: homeData.social_tiktok,
              social_youtube: homeData.social_youtube,
              social_snapchat: homeData.social_snapchat,
            }}
          />
        )}
    </HomePageWrapper>
  );
}
