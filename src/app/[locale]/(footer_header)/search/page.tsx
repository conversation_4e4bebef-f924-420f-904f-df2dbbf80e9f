"use client";
import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { searchNews } from "../../../../lib/api";
import translations from "@/lib/locales";

interface NewsResult {
    id: number;
    slug: string;
    title: string;
    intro: string;
    content: string;
    image: string;
    image_full: string;
    published_at: string;
    category: {
        id: number;
        title: string;
        slug: string;
    };
    type: number;
}

interface SearchResponse {
    status: boolean;
    news: NewsResult[];
    pagination: {
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
        next_page_url: string | null;
        prev_page_url: string | null;
    };
}

export default function SearchPage({ params }: { params: Promise<{ locale: string }> }) {
    const { locale } = React.use(params);
    const t = (key: string, vars?: Record<string, any>) => {
        let str = translations[locale]?.[key] || key;
        if (vars) {
            Object.entries(vars).forEach(([k, v]) => {
                str = str.replace(new RegExp(`{${k}}`, 'g'), v);
            });
        }
        return str;
    };

    const searchParams = useSearchParams();
    const query = searchParams?.get('q') || '';
    const [results, setResults] = useState<NewsResult[]>([]);
    const [loading, setLoading] = useState(false);
    const [searchInput, setSearchInput] = useState(query);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalResults, setTotalResults] = useState(0);
    const [totalPages, setTotalPages] = useState(0);
    const [pagination, setPagination] = useState<SearchResponse['pagination'] | null>(null);

    // Perform search using the real API
    const performSearch = async (searchQuery: string, page: number = 1) => {
        if (!searchQuery.trim()) {
            setResults([]);
            setTotalResults(0);
            setTotalPages(0);
            setPagination(null);
            return;
        }

        setLoading(true);
        try {
            const response: SearchResponse = await searchNews(searchQuery.trim(), locale, page, 12);

            if (response.status && response.news) {
                setResults(response.news);
                setTotalResults(response.pagination.total);
                setTotalPages(response.pagination.last_page);
                setPagination(response.pagination);
            } else {
                setResults([]);
                setTotalResults(0);
                setTotalPages(0);
                setPagination(null);
            }
        } catch (error) {
            console.error('Search error:', error);
            setResults([]);
            setTotalResults(0);
            setTotalPages(0);
            setPagination(null);
        } finally {
            setLoading(false);
        }
    };

    const handleSearchSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (searchInput.trim()) {
            performSearch(searchInput.trim(), 1);
            setCurrentPage(1);
            // Update URL without full page reload
            const newUrl = `/${locale}/search?q=${encodeURIComponent(searchInput.trim())}`;
            window.history.pushState({}, '', newUrl);
        }
    };

    // Handle pagination
    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        performSearch(searchInput.trim(), page);
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    // Search when query changes
    useEffect(() => {
        if (query) {
            setSearchInput(query);
            performSearch(query, 1);
            setCurrentPage(1);
        }
    }, [query]);

    return (
        <div className="search-page-content min-h-screen bg-gray-50 pt-24 lg:pt-[175px] pb-16">
            {/* Header */}
            <div className=" text-white py-8">
                <div className="container mx-auto px-4">
                    {/* Search Form */}
                    <div className="max-w-2xl mx-auto">
                        <form onSubmit={handleSearchSubmit} className="relative">
                            <input
                                type="text"
                                value={searchInput}
                                onChange={(e) => setSearchInput(e.target.value)}
                                placeholder={t('search') + '...'}
                                className="w-full px-4 py-3 pr-12 text-gray-900 rounded-lg border-2 border-[#DFB254] focus:outline-none focus:border-[#C60428] placeholder:text-gray-400 transition-colors duration-200 hover:border-[#DFB254]"
                                dir={locale === 'ar' ? 'rtl' : 'ltr'}
                                style={{ boxShadow: 'none' }}
                            />
                            <button
                                type="submit"
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-[#DFB254] text-white p-2 rounded-lg hover:bg-[#c8a049] transition-colors"
                                style={{ right: locale === 'ar' ? 'auto' : '0.5rem', left: locale === 'ar' ? '0.5rem' : 'auto' }}
                            >
                                <Image
                                    src="/search_icon.svg"
                                    alt="Search"
                                    width={20}
                                    height={20}
                                    className="w-5 h-5"
                                />
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            {/* Results */}
            <div className="container mx-auto px-4 py-8">
                {query && (
                    <div className="mb-6">
                        <h2 className="text-xl font-semibold text-gray-900 mb-2">
                            {t('search_results_for', { query })}
                        </h2>
                        <p className="text-gray-600">
                            {t('found_results', { count: totalResults })}
                        </p>
                    </div>
                )}

                {/* Loading State */}
                {loading && (
                    <div className="text-center py-12">
                        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#C60428] mb-4"></div>
                        <p className="text-gray-600">
                            {locale === 'ar' ? 'جاري البحث...' : 'Searching...'}
                        </p>
                    </div>
                )}

                {/* No Results */}
                {!loading && query && results.length === 0 && (
                    <div className="text-center py-12">
                        <div className="mb-4">
                            <Image
                                src="/search_icon.svg"
                                alt="No results"
                                width={64}
                                height={64}
                                className="mx-auto opacity-50"
                            />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                            {t('no_results_found')}
                        </h3>
                        <p className="text-gray-600">
                            {t('try_different_keywords')}
                        </p>
                    </div>
                )}

                {/* Results Grid */}
                {!loading && results.length > 0 && (
                    <div className="space-y-4">
                        {results.map((result) => (
                            <Link
                                key={result.id}
                                href={`/${locale}/news/article/${result.slug}`}
                                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow block"
                            >
                                <div className="p-4">
                                    <div className="flex gap-4">
                                        {/* Image Thumbnail */}
                                        <div className="flex-shrink-0">
                                            <div className="w-24 h-24 rounded-lg overflow-hidden bg-gray-100">
                                                {result.image || result.image_full ? (
                                                    <Image
                                                        src={result.image || result.image_full}
                                                        alt={result.title}
                                                        width={96}
                                                        height={96}
                                                        className="w-full h-full object-cover"
                                                    />
                                                ) : (
                                                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                                        <Image
                                                            src="/news_placeholder.svg"
                                                            alt="No image"
                                                            width={32}
                                                            height={32}
                                                            className="w-8 h-8 opacity-40"
                                                        />
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        
                                        {/* Content */}
                                        <div className="flex-1 min-w-0">
                                            <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-[#C60428] transition-colors">
                                                {result.title}
                                            </h3>
                                            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                                                {result.intro}
                                            </p>
                                            <div className="flex justify-between items-center">
                                                <span className="text-xs text-white font-medium bg-[#C60428] px-3 py-1 rounded">
                                                    {result.category?.title || 'News'}
                                                </span>
                                                <span className="text-xs text-gray-500">
                                                    {new Date(result.published_at).toLocaleDateString(
                                                        locale === 'ar' ? 'ar-EG' : 'en-US'
                                                    )}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Link>
                        ))}
                    </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && pagination && (
                    <div className="flex justify-center mt-8">
                        <div className="flex space-x-2">
                            {/* Previous Button */}
                            {pagination.prev_page_url && (
                                <button
                                    onClick={() => handlePageChange(currentPage - 1)}
                                    className="px-4 py-2 rounded bg-white text-gray-700 hover:bg-gray-100 border"
                                >
                                    {locale === 'ar' ? 'السابق' : 'Previous'}
                                </button>
                            )}

                            {/* Page Numbers */}
                            {[...Array(Math.min(totalPages, 10))].map((_, index) => {
                                let pageNum = index + 1;

                                // Smart pagination: show pages around current page
                                if (totalPages > 10) {
                                    if (currentPage <= 5) {
                                        pageNum = index + 1;
                                    } else if (currentPage >= totalPages - 4) {
                                        pageNum = totalPages - 9 + index;
                                    } else {
                                        pageNum = currentPage - 4 + index;
                                    }
                                }

                                return (
                                    <button
                                        key={pageNum}
                                        onClick={() => handlePageChange(pageNum)}
                                        className={`px-4 py-2 rounded ${currentPage === pageNum
                                                ? 'bg-[#C60428] text-white'
                                                : 'bg-white text-gray-700 hover:bg-gray-100 border'
                                            }`}
                                    >
                                        {pageNum}
                                    </button>
                                );
                            })}

                            {/* Next Button */}
                            {pagination.next_page_url && (
                                <button
                                    onClick={() => handlePageChange(currentPage + 1)}
                                    className="px-4 py-2 rounded bg-white text-gray-700 hover:bg-gray-100 border"
                                >
                                    {locale === 'ar' ? 'التالي' : 'Next'}
                                </button>
                            )}
                        </div>
                    </div>
                )}

                {/* No Query State */}
                {!query && !loading && (
                    <div className="text-center py-12">
                        <div className="mb-4">
                            <Image
                                src="/search_icon.svg"
                                alt="Start searching"
                                width={64}
                                height={64}
                                className="mx-auto opacity-50"
                            />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                            {t('start_searching')}
                        </h3>
                        <p className="text-gray-600">
                            {t('enter_search_query')}
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
}
