import { Metadata } from 'next';
import { getCachedNewsArticle } from '@/lib/cache-helpers';
import translations from '@/lib/locales';
import { notFound } from 'next/navigation';
import Image from 'next/image';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.alahlyegypt.com';

// ISR configuration - revalidate every 5 minutes for dynamic content that might not exist
export const revalidate = 300;

// Use cached news article fetcher
const getNewsArticle = getCachedNewsArticle;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>;
}): Promise<Metadata> {
  const { locale, slug } = await params;
  const t = (key: string) => translations[locale]?.[key] || key;
  const res = await getNewsArticle(`/api/news/view/${slug}`, locale, [`news-${slug}`]);

  if (!res.news) return {};

  const metaTitle = res.news.meta_title || res.news.title;
  const metaDescription = res.news.meta_description || res.news.intro;
  const metaKeywords = res.news.meta_keyword || undefined;
  const canonicalUrl = `${siteUrl}/${locale}/club/news/${slug}`;

  return {
    title: metaTitle,
    description: metaDescription,
    keywords: metaKeywords,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      images: res.news.image_full ? [res.news.image_full] : [],
      url: canonicalUrl,
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: res.news.image_full ? [res.news.image_full] : [],
    },
  };
}

export default async function NewsArticlePage({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>;
}) {
  const { locale, slug } = await params;
  const t = (key: string) => translations[locale]?.[key] || key;

  const data = await getNewsArticle(`/api/news/view/${slug}`, locale, [`news-${slug}`]);

  if (!data) {
    return notFound();
  }

  if (!data?.news) return notFound();

  const { news } = data;

  return (
    <main className="flex flex-col gap-3 bg-[#F7F7F7] pt-24 pb-24 lg:pt-[175px]">
      {/* Breadcrumb */}
      <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
        <ol className="flex flex-wrap items-center gap-1">
          <li>
            <a href={`/${locale}/club`} className="hover:underline text-[#C60428] font-bold">{t('club') || 'النادي'}</a>
          </li>
          <li className="mx-1">|</li>
          <li>
            <a href={`/${locale}/club/news`} className="hover:underline text-[#C60428] font-bold">{t('club_news') || 'أخبار النادى'}</a>
          </li>
          <li className="mx-1">|</li>
          <li>
            {news.title}
          </li>
        </ol>
      </nav>
      {/* End Breadcrumb */}
      <div className="flex flex-col gap-3 row-start-2 items-center sm:items-start w-full px-2">
        <div className="flex flex-col lg:flex-row gap-8 w-full container mx-auto px-0 sm:px-2 md:px-4">
          <div className="flex-1 min-w-0">
            <div className="mb-4">
              {news.image_full && (
                <div className="mb-4 w-full aspect-video relative rounded overflow-hidden">
                  <Image src={news.image_full} alt={news.title} fill className="object-cover" />
                </div>
              )}
              <div className="text-xs text-gray-500 mb-2">
                {new Date(news.published_at).toLocaleString(
                  locale.replace('_', '-'),
                  { day: 'numeric', month: 'long', year: 'numeric', hour: '2-digit', minute: '2-digit' }
                )}
              </div>
              <h1 className="text-2xl sm:text-3xl font-bold mb-4 text-black">{news.title}</h1>
              {news.content && (
                <div
                  className="prose prose-lg max-w-none text-black mb-8"
                  dangerouslySetInnerHTML={{ __html: news.content }} />
              )}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
