import PermitRequestPage from '@/components/club/PermitRequestPage';
import Footer from '@/components/footer';
import translations from "@/lib/locales";
import type { Metadata } from "next";
const defaultLocale = 'ar';
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.alahlyegypt.com';

export async function generateMetadata({ params }: { params: any }): Promise<Metadata> {
  // Await params if it's a Promise (for dynamic routes)
  const resolvedParams = typeof params?.then === 'function' ? await params : params;
  const locale = resolvedParams?.locale || defaultLocale;
  const t = (key: string) => translations[locale]?.[key] || key;
  return {
    title: t("permit_request") + " | " + t("site_title"),
    description: t("site_description") || "A website dedicated for Al Ahly SC",
    alternates: {
      canonical: `${siteUrl}/${locale}`,
    },
  };
}

export default async function PermitPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = (key: string) => translations[locale]?.[key] || key;
  return (
    <main className="flex bg-[#f6f6f6] flex-col min-h-fit pt-24 pb-12 lg:pt-[175px]">
      {/* Breadcrumb */}
      <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
        <ol className="flex flex-wrap items-center gap-1">
          <li>
            <a href={`/${locale}/club`} className="hover:underline text-[#C60428] font-bold">{t('club') || 'النادي'}</a>
          </li>
          <li className="mx-1">|</li>
          <li>
            {t("permit_request") || "طلب تصريح"}
          </li>
        </ol>
      </nav>
      {/* End Breadcrumb */}
      <PermitRequestPage locale={locale} />
    </main>
  );
}
