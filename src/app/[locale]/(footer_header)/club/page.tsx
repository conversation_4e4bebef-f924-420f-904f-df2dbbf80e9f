import Link from 'next/link';
import navbarData from '@/data/navbar.json';
import translations from '@/lib/locales';

// ISR configuration - revalidate every 24 hours for static content
export const revalidate = 86400;

export default async function ClubMainPage({ params }: { params: Promise<{ locale: string }> }) {
    const { locale } = await params;
    const t = (key: string) => translations[locale]?.[key] || key;

    // Find the 'club' section in navbarData.bottom
    const clubSection = navbarData.bottom.find((item: any) => item.label === 'club');
    const links = clubSection?.sublinks || [];

    return (
        <main className="w-full mx-auto py-8 px-2 pt-24 lg:pt-[140px] pb-16 bg-gradient-to-br from-[#F7F7F7] to-[#fff] min-h-screen">
            <h1 className="text-4xl font-extrabold mt-4 mb-10 text-center text-[#C60428] tracking-tight drop-shadow flex items-center justify-center gap-4">
                <img src="/logo.png" alt="Al Ahly Logo" className="w-14 h-14 inline-block align-middle drop-shadow-lg animate-bounce-slow" />
                {t('club')}
            </h1>
            <ul className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
                {links.map((link: any, idx: number) => {
                    const isExternal = link.url && (link.url.startsWith('http://') || link.url.startsWith('https://'));
                    const href = isExternal ? link.url : `/${locale}${link.url}`;
                    return (
                        <li key={link.url} className="flex">
                            {isExternal ? (
                                <a
                                    href={href}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="flex-1 group p-6 bg-white/95 rounded-2xl shadow-xl border-2 border-[#C60428] hover:border-[#DFB254] hover:shadow-2xl hover:-translate-y-1 transition-all duration-200 flex flex-col items-center justify-center text-center gap-3 relative overflow-hidden min-h-0 focus:ring-2 focus:ring-[#DFB254] focus:z-10"
                                    style={{ minHeight: '110px' }}
                                >
                                    <span className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-[#DFB254]/10 to-[#C60428]/10 pointer-events-none rounded-2xl" />
                                    <span className="text-2xl font-bold text-[#C60428] group-hover:text-[#DFB254] transition-colors duration-200 drop-shadow-sm tracking-wide">
                                        {t(link.label)}
                                    </span>
                                    <span className="w-14 h-1 bg-[#DFB254] rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 mt-2"></span>
                                </a>
                            ) : (
                                <Link
                                    href={href}
                                    className="flex-1 group p-6 bg-white/95 rounded-2xl shadow-xl border-2 border-[#C60428] hover:border-[#DFB254] hover:shadow-2xl hover:-translate-y-1 transition-all duration-200 flex flex-col items-center justify-center text-center gap-3 relative overflow-hidden min-h-0 focus:ring-2 focus:ring-[#DFB254] focus:z-10"
                                    style={{ minHeight: '110px' }}
                                >
                                    <span className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-[#DFB254]/10 to-[#C60428]/10 pointer-events-none rounded-2xl" />
                                    <span className="text-2xl font-bold text-[#C60428] group-hover:text-[#DFB254] transition-colors duration-200 drop-shadow-sm tracking-wide">
                                        {t(link.label)}
                                    </span>
                                    <span className="w-14 h-1 bg-[#DFB254] rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 mt-2"></span>
                                </Link>
                            )}
                        </li>
                    );
                })}
            </ul>
        </main>
    );
}

/* Tailwind custom animation (add to your global CSS if not present):
@keyframes bounce-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8px); }
}
.animate-bounce-slow {
  animation: bounce-slow 2.5s infinite;
}
*/
