"use client";

import React from "react";
import { useEffect, useState } from "react";
import translations from "@/lib/locales";
import Image from "next/image";
import { fetchApi } from "@/lib/api";

export default function MembershipsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = React.use(params);
  const t = (key: string) => translations[locale]?.[key] || key;
  const [memberships, setMemberships] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMembershipIdx, setSelectedMembershipIdx] = useState(0);

  useEffect(() => {
    async function fetchMemberships() {
      setLoading(true);
      setError(null);
      try {
        const data = await fetchApi("/api/club/memberships", { headers: { "X-localization": locale } });
        if (data.memberships && Array.isArray(data.memberships)) {
          setMemberships(data.memberships);
        } else {
          setError(t("no_memberships"));
        }
      } catch (err) {
        setError(t("no_memberships"));
      } finally {
        setLoading(false);
      }
    }
    fetchMemberships();
  }, [locale]);

  const selectedMembership = memberships[selectedMembershipIdx];
  const selectedRequirements = selectedMembership?.requirements || [];
  const selectedFaqs = selectedMembership?.faqs || [];

  return (
    <main className="flex flex-col bg-[#f6f6f6] min-h-fit pt-24 pb-12 lg:pt-[175px]">
      {/* Breadcrumb */}
      <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
        <ol className="flex flex-wrap items-center gap-1">
          <li>
            <a href={`/${locale}/club`} className="hover:underline text-[#C60428] font-bold">{t('club') || 'النادي'}</a>
          </li>
          <li className="mx-1">|</li>
          <li>
            {t("membership") || "عضوية النادي"}
          </li>
        </ol>
      </nav>
      {/* End Breadcrumb */}
      {/* Header with stadium image and overlay title */}
      <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
        <Image
          src="/img/default_bg.png"
          alt={t("club_memberships") || "عضوية النادى الأهلي"}
          fill
          className="object-cover w-full h-full"
          priority
        />
        <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
            {(t("club_memberships") || "عضوية النادى الأهلي").split('\n').map((line: string, idx: number, arr: string[]) => (
              <React.Fragment key={idx}>
                {line}
                {idx !== arr.length - 1 && <br />}
              </React.Fragment>
            ))}
          </h2>
        </div>
      </div>
      {/* memberships grid */}
      <section className="max-w-6xl mx-auto w-full py-12 px-4">
        {loading ? (
          <div className="text-center text-[#C60428] py-10 text-lg font-bold">{t("loading")}</div>
        ) : error ? (
          <div className="text-center text-red-600 py-10 text-lg font-bold">{error}</div>
        ) : (
          <div className="flex flex-col gap-8">
            {/* Membership Tabs */}
            <div className="flex flex-wrap justify-center gap-4 mb-8 border-b border-gray-200">
              {memberships.map((m, idx) => (
                <button
                  key={m.id}
                  className={`px-4 py-2 font-bold rounded-t-lg transition-colors duration-200 ${selectedMembershipIdx === idx ? 'bg-[#C60428] text-white' : 'bg-[#f3f3f3] text-[#C60428]'} shadow-sm`}
                  onClick={() => setSelectedMembershipIdx(idx)}
                >
                  {m.title}
                </button>
              ))}
            </div>
            {/* Prices Table */}
            <div className="overflow-x-auto bg-white rounded-xl shadow p-6 mb-6">
              <table className="min-w-full text-center border-separate border-spacing-y-2">
                <thead>
                  <tr className="bg-[#f3f3f3] text-[#C60428]">
                    <th className="py-2 px-4 rounded-s-lg">{t('education_level_high') || 'المؤهلات العليا'}</th>
                    <th className="py-2 px-4 rounded-e-lg">{t('education_level_non_high') || 'غير المؤهلات العليا'}</th>
                  </tr>
                </thead>
                <tbody>
                  {selectedMembership && (
                    <tr key={selectedMembership.id} className="bg-white">
                      <td className="py-2 px-4 border-b border-gray-100 font-bold">
                        {selectedMembership.prices?.high_education?.[0]?.price?.toLocaleString() || '-'}
                      </td>
                      <td className="py-2 px-4 border-b border-gray-100 font-bold">
                        {selectedMembership.prices?.non_high_education?.[0]?.price?.toLocaleString() || '-'}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
              <div className="text-xs text-gray-500 mt-2">{t('all_prices_include_tax') || 'جميع الأسعار شاملة ضريبة القيمة المضافة'}</div>
              <div className="text-xs text-gray-400 mt-1">{t('terms_and_conditions') || 'تطبق الشروط والأحكام'}</div>
            </div>
            {/* Requirements Section */}
            <div className="mb-8">
              <div className="font-bold text-[#C60428] text-lg mb-2">{t('required_documents') || 'المستندات المطلوبة'}</div>
              <ul className="list-disc list-inside text-[#222] text-sm bg-white rounded-xl shadow p-6">
                {selectedRequirements.length > 0 ? (
                  selectedRequirements.map((req: { id: string | number; requirement: string }) => (
                    <li key={req.id}>{req.requirement}</li>
                  ))
                ) : (
                  <li>{t('no_requirements') || 'لا توجد مستندات مطلوبة'}</li>
                )}
              </ul>
            </div>
            {/* FAQs Section */}
            <div className="mb-8">
              <div className="font-bold text-[#C60428] text-lg mb-2">{t('faqs') || 'أسئلة شائعة'}</div>
              <div className="bg-white rounded-xl shadow p-6">
                {selectedFaqs.length > 0 ? (
                  selectedFaqs.map((faq: { id: string | number; question: string; answer: string }) => (
                    <div className="mb-2" key={faq.id}>
                      <div className="font-bold text-[#C60428]">{faq.question}</div>
                      <div className="text-[#222] text-sm">{faq.answer}</div>
                    </div>
                  ))
                ) : (
                  <div className="text-[#222] text-sm">{t('no_faqs') || 'لا توجد أسئلة شائعة'}</div>
                )}
              </div>
            </div>
            {/* Membership Renewal Section */}
            <div className="mb-8">
              <div className="font-bold text-[#C60428] text-lg mb-2">{t('membership_renewal') || 'تجديد العضوية'}</div>
              <div className="bg-white rounded-xl shadow p-6 text-[#222] text-sm">
                {t('membership_renewal_instructions')}
              </div>
            </div>
          </div>
        )}
      </section>
    </main>
  );
}
