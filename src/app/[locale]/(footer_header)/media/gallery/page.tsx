import { fetchApi } from '@/lib/api';
import translations from '@/lib/locales';
import NewsletterForm from '@/components/newsletter-form';
import Image from 'next/image';
import NewsPage from '@/components/news-grid';
import React from 'react';

export default async function GalleryHomePage({ params }: { params: Promise<{ locale: string }> }) {
    const { locale } = await params;
    const t = (key: string) => translations[locale]?.[key] || key;
    let data;
    try {
        data = await fetchApi('/api/news/category/all?type=3', { headers: { 'X-localization': locale } });
    } catch {
        return (
            <main className="flex flex-col min-h-fit pt-24 lg:pt-[125px] pb-24 items-center justify-center">
                <div className="text-red-600 text-xl font-bold mt-20">{t('error_message') || 'حدث خطأ أثناء تحميل الأخبار.'}</div>
            </main>
        );
    }
    if (!data || !data.categories || !data.news) {
        return (
            <main className="flex flex-col min-h-fit pt-24 lg:pt-[125px] pb-24 items-center justify-center">
                <div className="text-red-600 text-xl font-bold mt-20">{t('error_message') || 'حدث خطأ أثناء تحميل الأخبار.'}</div>
            </main>
        );
    }
    const { categories, news, pagination } = data;

    return (
        <main className="flex flex-col bg-[#f6f6f6] min-h-fit pt-24 pb-12 lg:pt-[125px] pb-24">
            {/* Header with stadium image and overlay title */}
            <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
                <Image
                    src="/img/default_bg.png"
                    alt={t("photo_library") || "مكتبة الصور"}
                    fill
                    className="object-cover w-full h-full"
                    priority
                />
                <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
                    <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
                        {(t("photo_library") || "مكتبة الصور").split('\n').map((line: string, idx: number, arr: string[]) => (
                            <React.Fragment key={idx}>
                                {line}
                                {idx !== arr.length - 1 && <br />}
                            </React.Fragment>
                        ))}
                    </h2>
                </div>
            </div>
            {/* Main Content and Sidebar */}
            <div className="container mt-4 mx-auto px-2 md:px-4 flex flex-col-reverse lg:flex-row gap-8">
                {/* Main Content */}
                <section className="flex-1 order-2 lg:order-1">
                    <NewsPage locale={locale} title={""} categories={categories} initialNews={news} initialType='3' initialPagination={pagination} />
                </section>
            </div>
        </main>
    );
}
