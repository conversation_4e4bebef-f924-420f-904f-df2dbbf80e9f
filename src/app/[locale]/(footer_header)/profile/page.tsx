"use client";

import { useEffect, useState } from "react";
import translations from "@/lib/locales";
import { fetchApi } from "@/lib/api";
import { usePathname, useRouter } from "next/navigation";
import Image from "next/image";

export default function ProfilePage() {
  const pathname = usePathname();
  const router = useRouter();
  const locale = pathname ? pathname.split("/")[1] : "ar";
  const t = (key: string) => translations[locale]?.[key] || key;

  const [user, setUser] = useState<any>(null);
  const [form, setForm] = useState({
    name: "",
    email: "",
    phone: "",
    profile_picture: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  // Add a new state for the file
  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const userStr = localStorage.getItem("user");
      if (userStr) {
        const userObj = JSON.parse(userStr);
        setUser(userObj);
        setForm({
          name: userObj.name || "",
          email: userObj.email || "",
          phone: userObj.phone || "",
          profile_picture: userObj.profile_picture || "",
        });
      }
    }
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    try {
      const token = localStorage.getItem("access_token");
      const formData = new FormData();
      formData.append("name", form.name);
      formData.append("email", form.email);
      formData.append("phone", form.phone);
      if (profilePictureFile) {
        formData.append("profile_picture", profilePictureFile);
      } else if (form.profile_picture) {
        formData.append("profile_picture", form.profile_picture);
      }
      const res = await fetchApi("/api/client/profile", {
        method: "PUT",
        // Do NOT set Content-Type for FormData, browser will set it with boundary
        headers: {
          "Accept": "application/json",
          "X-localization": locale,
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });
      if (res.status === 200) {
        const result = await res.json();
        // Support both {user, ...} and direct user object
        const updatedUser = result.user || result;
        localStorage.setItem("user", JSON.stringify(updatedUser));
        setUser(updatedUser);
        setSuccess(t("profile_update_success"));
        setTimeout(() => router.refresh(), 500);
      } else {
        const data = await res.json();
        if (data.errors) {
          const errorMessages = Object.values(data.errors).flat().join("\n");
          setError(errorMessages);
        } else {
          setError(data.message || t("profile_update_error"));
        }
      }
    } catch (err) {
      setError(t("profile_update_error"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setProfilePictureFile(file);
    setForm((prev) => ({ ...prev, profile_picture: "" }));
    setError(null);
    setSuccess(null);
  };

  if (!user) return <div className="p-8 text-center">{t("not_logged_in")}</div>;

  return (
    <main className="min-h-screen full-w items-center justify-center bg-[#333333] bg-[url('/img/socials-bg.png')] bg-cover pt-[200px] pb-20 p-4">
      <div className="max-w-lg mx-auto p-6 bg-white rounded-lg shadow">
        <h1 className="text-2xl font-bold mb-6 text-center">{t("profile")}</h1>
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          <div className="flex flex-col items-center gap-2 mb-4">
            <Image
              src={profilePictureFile ? URL.createObjectURL(profilePictureFile) : (form.profile_picture || "/logo.png")}
              alt="Profile"
              width={80}
              height={80}
              className="rounded-full bg-gray-200"
            />
            <input
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className="w-full border rounded px-3 py-2"
            />
          </div>
          <input
            type="text"
            name="name"
            value={form.name}
            onChange={handleChange}
            placeholder={t("fuLL_name")}
            className="w-full border rounded px-3 py-2"
          />
          <input
            type="email"
            name="email"
            value={form.email}
            onChange={handleChange}
            placeholder={t("email")}
            className="w-full border rounded px-3 py-2"
          />
          <input
            type="text"
            name="phone"
            value={form.phone}
            onChange={handleChange}
            placeholder={t("phone")}
            className="w-full border rounded px-3 py-2"
          />
          {error && <div className="text-red-600 text-sm">{error}</div>}
          {success && <div className="text-green-600 text-sm">{success}</div>}
          <button
            type="submit"
            className={`w-full py-3 rounded font-bold text-white ${isLoading ? "bg-[#a00320] cursor-not-allowed" : "bg-[#EC2028] hover:bg-[#d11a2a]"} transition-colors duration-300`}
            disabled={isLoading}
          >
            {isLoading ? t("saving") : t("save_changes")}
          </button>
        </form>
      </div>
    </main>
  );
}
