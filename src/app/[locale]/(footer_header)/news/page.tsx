import { fetchApi } from '@/lib/api';
import translations from '@/lib/locales';
import NewsletterForm from '@/components/newsletter-form';
import VideosSection from '@/components/videos-section';
import NewsPage from '@/components/news-grid';
import type { NewsItem } from '@/components/news-card';
import Link from 'next/link';
import RelatedNewsWidget from '@/components/RelatedNewsWidget';
import NewsImageWithFallback from '@/components/NewsImageWithFallback';

export default async function NewsHomePage({ params }: { params: Promise<{ locale: string }> }) {
    const { locale } = await params;
    const t = (key: string) => translations[locale]?.[key] || key;
    let data;
    try {
        data = await fetchApi('/api/widgets/news', { headers: { 'X-localization': locale } });
    } catch {
        return (
            <main className="flex flex-col min-h-fit pt-24 lg:pt-[125px] pb-24 items-center justify-center">
                <div className="text-red-600 text-xl font-bold mt-20">{t('error_message') || 'حدث خطأ أثناء تحميل الأخبار.'}</div>
            </main>
        );
    }
    if (!data || !data.data) {
        return (
            <main className="flex flex-col min-h-fit pt-24 lg:pt-[125px] pb-24 items-center justify-center">
                <div className="text-red-600 text-xl font-bold mt-20">{t('error_message') || 'حدث خطأ أثناء تحميل الأخبار.'}</div>
            </main>
        );
    }
    const { latest_news, featured, most_viewed, videos, categories, first_category_news, social_x, social_tiktok, social_instagram, social_facebook, social_youtube, social_snapchat, banners } = data.data;

    // Collect available social links
    const socialLinks = [
        { key: 'social_facebook', icon: '/socials/Facebook-icon.png', alt: 'Facebook', url: social_facebook },
        { key: 'social_instagram', icon: '/socials/Instagram-icon.png', alt: 'Instagram', url: social_instagram },
        { key: 'social_x', icon: '/socials/X-icon.png', alt: 'X', url: social_x },
        { key: 'social_tiktok', icon: '/socials/TikTok-icon.png', alt: 'TikTok', url: social_tiktok },
        { key: 'social_youtube', icon: '/socials/Youtube-icon.png', alt: 'YouTube', url: social_youtube },
        { key: 'social_snapchat', icon: '/socials/Snapchat-icon.png', alt: 'Snapchat', url: social_snapchat },
    ].filter(link => !!link.url);

    // Extract banners safely
    const newsWideTopBanner = banners?.news_wide_top?.[0];
    const newsSidebarTopBanner = banners?.news_sidebar_vertical_top?.[0];
    const newsSidebarBottomBanner = banners?.news_sidebar_vertical_bottom?.[0];

    return (
        <main className="flex flex-col gap-8 min-h-fit bg-[#F7F7F7] pt-36 pb-24 lg:pt-[175px]">
            {/* Hero/Featured Section */}
            <section className="w-full  mx-auto px-2 md:px-4 grid grid-cols-1 lg:grid-cols-3 gap-6 mb-10 items-stretch">
                {/* Main featured news - left side (large) */}

                {featured && featured[0] && (
                    <>
                        <Link
                            href={`/${locale}/news/article/${featured[0].slug}`}
                            className="col-span-1 lg:col-span-2 relative rounded-xl overflow-hidden flex flex-col justify-end bg-white shadow h-44 md:h-[440px] group"
                        >
                            <NewsImageWithFallback
                                src={featured[0].image_full || '/img/default_placeholder.png'}
                                alt={featured[0].title ?? featured[0].alt_text}
                                fill
                                sizes="100vw"
                                style={{ objectFit: 'cover', objectPosition: 'top', width: '100%', height: '100%' }}
                                className="group-hover:scale-105 transition duration-300"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent z-10" />
                            <div className="relative z-20 p-8 pb-6">
                                <div className="flex items-center gap-2 mb-2">
                                    <span className="bg-[#EC2028] text-white text-sm px-3 py-1 rounded">
                                        {featured[0].category?.title || ''}
                                    </span>
                                    <span className="text-xs text-white">
                                        {featured[0].published_at ?
                                            (() => {
                                                return new Date(featured[0].published_at).toLocaleDateString(
                                                    locale.replace('_', '-'),
                                                    { day: 'numeric', month: 'long', year: 'numeric' }
                                                );
                                            })()
                                            : ''}
                                    </span>
                                </div>
                                <h2 className="font-bold text-base text-white line-clamp-2">
                                    {featured[0].title}
                                </h2>
                                <div className="text-lg text-white/90 line-clamp-2 drop-shadow">
                                    {featured[0].intro}
                                </div>
                            </div>
                        </Link>
                    </>
                )}

                {/* Right side: 2 stacked small + 1 wide on top */}
                <div className="grid grid-cols-2 grid-rows-2 gap-4 col-span-1 h-full">
                    {/* Top wide item spans 2 columns */}
                    {featured && featured[1] && (
                        <Link
                            key={featured[1].id}
                            href={`/${locale}/news/article/${featured[1].slug}`}
                            className="relative group rounded-xl overflow-hidden col-span-2 h-44 md:h-52 flex flex-col justify-end bg-white shadow hover:shadow-lg transition"
                        >
                            <NewsImageWithFallback
                                src={featured[1].image_full || '/img/default_placeholder.png'}
                                alt={featured[1].title}
                                fill
                                sizes="100vw"
                                style={{ objectFit: 'cover', objectPosition: 'top', width: '100%', height: '100%' }}
                                className="group-hover:scale-105 transition duration-300"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent z-10" />
                            <div className="relative z-20 p-3">
                                <div className="flex items-center gap-2 mb-1">
                                    <span className="bg-[#EC2028] text-white text-xs px-2 py-0.5 rounded">
                                        {featured[1].category?.title || ''}
                                    </span>
                                    <span className="text-xs text-white">
                                        {featured[1].published_at ?
                                            (() => {
                                                return new Date(featured[1].published_at).toLocaleDateString(
                                                    locale.replace('_', '-'),
                                                    { day: 'numeric', month: 'long', year: 'numeric' }
                                                );
                                            })()
                                            : ''}
                                    </span>
                                </div>
                                <div className="font-bold text-base text-white line-clamp-2">
                                    {featured[1].title}
                                </div>
                            </div>
                        </Link>
                    )}

                    {/* Two small items */}
                    {featured && featured.slice(2, 4).map((item: NewsItem) => (
                        <Link
                            key={item.id}
                            href={`/${locale}/news/article/${item.slug}`}
                            className="relative group rounded-xl overflow-hidden h-44 md:h-52 flex flex-col justify-end bg-white shadow hover:shadow-lg transition"
                        >
                            <NewsImageWithFallback
                                src={item.image_full || '/img/default_placeholder.png'}
                                alt={item.title}
                                fill
                                sizes="100vw"
                                style={{ objectFit: 'cover', objectPosition: 'top', width: '100%', height: '100%' }}
                                className="group-hover:scale-105 transition duration-300"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent z-10" />
                            <div className="relative z-20 p-3">
                                <div className="flex items-center gap-2 mb-1">
                                    <span className="bg-[#EC2028] text-white text-xs px-2 py-0.5 rounded">
                                        {item.category?.title || ''}
                                    </span>
                                    <span className="text-xs text-white">
                                        {item.published_at
                                            ? new Date(item.published_at).toLocaleDateString(
                                                locale.replace('_', '-'),
                                                { day: 'numeric', month: 'long', year: 'numeric' }
                                            )
                                            : ''}
                                    </span>
                                </div>
                                <div className="font-bold text-base text-white line-clamp-2">
                                    {item.title}
                                </div>
                            </div>
                        </Link>
                    ))}
                </div>
            </section>

            {/* Ad Banner (Wide Top) */}
            {banners && newsWideTopBanner && newsWideTopBanner.image && newsWideTopBanner.url && (
                <div className="w-full h-fit">
                    <a href={newsWideTopBanner.url} target="_blank" rel="noopener noreferrer">
                        <img src={newsWideTopBanner.image} alt={newsWideTopBanner.title} className="w-full" />
                    </a>
                </div>
            )}
            {/* Main Content and Sidebar */}
            <div className="w-full  mx-auto px-2 md:px-4 flex flex-col-reverse lg:flex-row gap-8">

                {/* Main Content */}
                <section className="flex-1 order-2 lg:order-1">
                    <NewsPage locale={locale} title={t('latest_news')} categories={categories} />
                </section>
                {/* Sidebar */}
                <aside className="w-full lg:w-80 flex-shrink-0 flex flex-col gap-6 mt-8 lg:mt-0 order-1 lg:order-2">
                    {/* Social Links */}
                    {socialLinks.length > 0 && (
                        <div className="bg-black rounded shadow p-4 flex flex-col items-center">
                            <h3 className="font-bold text-lg mb-3 text-white">{t('stay_connected')}</h3>
                            <div className="flex flex-col gap-4 w-full">
                                {/* Follow Section */}
                                <div className="flex flex-col items-center gap-2">
                                    <div className="flex gap-3">
                                        {socialLinks.map(link => (
                                            <Link key={link.key} href={link.url} target="_blank" title={link.alt} rel="noopener noreferrer">
                                                <img src={link.icon} alt={link.alt} className="w-8 h-8" />
                                            </Link>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {/* Most Viewed News */}
                    <RelatedNewsWidget related={most_viewed} t={t} locale={locale} title={t('top_trending')} />

                    {/* Ad Space (Sidebar Top) */}
                    {banners && newsSidebarTopBanner && newsSidebarTopBanner.image && newsSidebarTopBanner.url && (
                        <div className="rounded flex flex-col items-center">
                            <div className="w-full bg-[#f1eeee]">
                                <a href={newsSidebarTopBanner.url} target="_blank" rel="noopener noreferrer">
                                    <img src={newsSidebarTopBanner.image} alt={newsSidebarTopBanner.title} className="w-full" />
                                </a>
                            </div>
                        </div>
                    )}
                </aside>
            </div>
            {/* Videos Section */}
            <VideosSection videos={videos} />

            {/* Main Content and Sidebar */}
            <div className="w-full mx-auto px-2 md:px-4 flex flex-col-reverse lg:flex-row gap-8">

                {/* Main Content */}
                <section className="flex-1 order-2 lg:order-1">
                    <NewsPage locale={locale} title={t('most_read')} displayCategories={false} />
                </section>
                {/* Sidebar */}
                <aside className="w-full lg:w-80 flex-shrink-0 flex flex-col gap-6 mt-8 lg:mt-0 order-1 lg:order-2">
                    {/* Ad Space (Sidebar Bottom) */}
                    {banners && newsSidebarBottomBanner && newsSidebarBottomBanner.image && newsSidebarBottomBanner.url && (
                        <div className="rounded flex flex-col items-center">
                            <div className="w-full bg-[#f1eeee]">
                                <a href={newsSidebarBottomBanner.url} target="_blank" rel="noopener noreferrer">
                                    <img src={newsSidebarBottomBanner.image} alt={newsSidebarBottomBanner.title} className="w-full" />
                                </a>
                            </div>
                        </div>
                    )}
                    {/* Newsletter Subscription */}
                    <div className="bg-white rounded shadow p-4">
                        <h3 className="font-bold text-lg mb-3 text-black">{t('subscribe_newsletter')}</h3>
                        <NewsletterForm tDict={translations[locale]} locale={locale} />
                    </div>
                </aside>
            </div>
        </main >
    );
}
