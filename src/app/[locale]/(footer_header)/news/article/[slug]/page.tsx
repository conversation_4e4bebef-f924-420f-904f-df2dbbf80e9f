import { Metadata } from 'next';
import { getCachedNewsArticle } from '@/lib/cache-helpers';
import translations from '@/lib/locales';
import { notFound } from 'next/navigation';
import NewsletterForm from '@/components/newsletter-form';
import Link from 'next/link';
import RelatedNewsWidget from '@/components/RelatedNewsWidget';
import { fixEmbedIframes } from '@/lib/utils';
import NewsImageWithFallback from '@/components/NewsImageWithFallback';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.alahlyegypt.com';

// ISR configuration - revalidate every hour
export const revalidate = 3600;



export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>;
}): Promise<Metadata> {
  const { locale, slug } = await params;
  const t = (key: string) => translations[locale]?.[key] || key;

  const data = await getCachedNewsArticle(`/api/news/view/${slug}`, locale, [`news-${slug}`]);
  if (!data?.news) return {};

  const news = data.news;
  const metaTitle = news.meta_title || news.title;
  const metaDescription = news.meta_description || news.intro;
  const metaKeywords = news.meta_keyword || undefined;
  const canonicalUrl = `${siteUrl}/${locale}/news/article/${slug}`;

  return {
    title: metaTitle,
    description: metaDescription,
    keywords: metaKeywords,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      images: news.image ? [news.image] : [],
      url: canonicalUrl,
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: news.image ? [news.image] : [],
    },
  };
}

export default async function NewsArticlePage({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>;
}) {
  const { locale, slug } = await params;
  const t = (key: string) => translations[locale]?.[key] || key;

  const data = await getCachedNewsArticle(`/api/news/view/${slug}`, locale, [`news-${slug}`]);
  
  if (!data?.news) {
    console.error(`News article not found: ${slug} (locale: ${locale})`);
    return notFound();
  }

  const { news, related } = data;
  const categories = data.categories || [];

  return (
    <main className="flex flex-col gap-3 min-h-fit bg-[#F7F7F7] pt-24 pb-32 lg:pt-[175px]">
      {/* Breadcrumb */}
      <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
        <ol className="flex flex-wrap items-center gap-1">
          <li>
            <a href={`/${locale}/news`} className="hover:underline text-[#C60428] font-bold">
              {t('news_center')}
            </a>
          </li>
          {news.category && (
            <>
              <li className="mx-1">|</li>
              <li>
                <a href={`/${locale}/news/category/${news.category.slug}`} className="hover:underline text-[#C60428] font-bold">
                  {news.category.title}
                </a>
              </li>
            </>
          )}
          <li className="mx-1">|</li>
          <li>
            {news.title?.trim() || t('not_available_in_this_language')}
          </li>
        </ol>
      </nav>

      {/* Content */}
      <div className="flex flex-col gap-3 row-start-2 items-center sm:items-start min-h-fit w-full px-2">
        <div className="flex flex-col lg:flex-row gap-8 w-full container mx-auto px-0 sm:px-2 md:px-4">
          <div className="flex-1 min-w-0">
            <div className="mb-4">
              <div className="mb-4 w-full aspect-video relative rounded overflow-hidden">
                <NewsImageWithFallback
                  src={news.image_full || '/img/default_placeholder.png'}
                  alt={news.title}
                  fill
                  className="object-cover"
                />
              </div>

              <div className="text-xs text-gray-500 mb-2">
                {new Date(news.published_at).toLocaleString(locale.replace('_', '-'), {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })} | {news.category?.title || ''}
              </div>

              <h1 className="text-2xl sm:text-3xl font-bold mb-4 text-black">{news.title || t('not_available_in_this_language')}</h1>

              {news.content?.trim() ? (
                <div
                  className="prose prose-lg max-w-none text-black mb-8"
                  style={{ overflow: 'hidden' }}
                  dangerouslySetInnerHTML={{ __html: fixEmbedIframes(news.content) }}
                />
              ) : (
                <div className="text-gray-500 mb-8">{t('not_available_in_this_language')}</div>
              )}
            </div>

            {/* Tags */}
            {news.tags?.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-6">
                {news.tags.map((tag: any, idx: number) => {
                  const tagLabel = typeof tag === 'string' ? tag : tag.title || tag.slug || '';
                  const tagSlug = typeof tag === 'string' ? tag : tag.slug || tag.title || '';
                  return (
                    <Link
                      key={tag.id || tagSlug || idx}
                      href={`/${locale}/news/tag/${encodeURIComponent(tagSlug)}`}
                      className="bg-[#EC2028] text-white px-3 py-1 rounded-full text-xs font-semibold hover:bg-[#b81a22] transition-colors"
                    >
                      #{tagLabel}
                    </Link>
                  );
                })}
              </div>
            )}

            {/* Share Buttons */}
            <div className="flex items-center gap-3 mb-8">
              <span className="text-sm font-semibold text-gray-700">{t('share')}</span>
              <a
                href={`https://x.com/intent/tweet?url=${encodeURIComponent(`${siteUrl}/${locale}/news/article/${slug}`)}&text=${encodeURIComponent(news.title)}`}
                target="_blank"
                rel="noopener noreferrer"
                className="w-9 h-9 flex items-center justify-center rounded-full bg-[#252323] hover:bg-[#1da1f2]"
              >
                <img src="/socials/X-icon.png" alt="X" className="w-5 h-5" />
              </a>
              <a
                href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(`${siteUrl}/${locale}/news/article/${slug}`)}`}
                target="_blank"
                rel="noopener noreferrer"
                className="w-9 h-9 flex items-center justify-center rounded-full bg-[#252323] hover:bg-[#1877f3]"
              >
                <img src="/socials/Facebook-icon.png" alt="Facebook" className="w-5 h-5" />
              </a>
              <a
                href={`https://wa.me/?text=${encodeURIComponent(news.title + ' ' + `${siteUrl}/${locale}/news/article/${slug}`)}`}
                target="_blank"
                rel="noopener noreferrer"
                className="w-9 h-9 flex items-center justify-center rounded-full bg-[#252323] hover:bg-[#25d366]"
              >
                <img src="/socials/Whatsapp-icon.png" alt="WhatsApp" className="w-5 h-5" />
              </a>
            </div>

            {/* Comments */}
            <div className="mt-8">
              <h2 className="text-lg font-bold mb-2 text-black">{t('comments')}</h2>
              <div className="text-gray-400">{t('no_comments')}</div>
            </div>
          </div>

          {/* Sidebar */}
          <aside className="hidden lg:block w-72 flex-shrink-0">
            {data.banner?.image && data.banner?.url && (
              <div className="rounded flex flex-col items-center mb-6">
                <div className="w-full bg-[#f1eeee]">
                  <a href={data.banner.url} target="_blank" rel="noopener noreferrer">
                    <img src={data.banner.image} alt={data.banner.title || t('ad_space')} className="w-full" />
                  </a>
                </div>
              </div>
            )}

            <div className="rounded p-4 mb-6">
              <h3 className="font-bold text-lg mb-3 text-black">{t('categories')}</h3>
              <ul className="space-y-2 w-full">
                {categories.map((cat: any) => (
                  <li key={cat.id}>
                    <Link
                      href={`/${locale}/news/category/${cat.slug}`}
                      className={`block px-2 py-1 rounded font-semibold ${
                        cat.id === news.category_id
                          ? 'bg-[#EC2028] text-white'
                          : 'hover:bg-gray-100 text-black'
                      }`}
                    >
                      {cat.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <RelatedNewsWidget related={related} t={t} locale={locale} />

            <div className="bg-white rounded shadow p-4 mb-6">
              <h3 className="font-bold text-lg mb-3 text-black">{t('subscribe_newsletter')}</h3>
              <NewsletterForm tDict={translations[locale]} locale={locale} />
            </div>
          </aside>
        </div>
      </div>
    </main>
  );
}
