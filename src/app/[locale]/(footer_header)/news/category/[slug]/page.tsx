import { fetchApi } from '@/lib/api';
import translations from '@/lib/locales';
import { notFound } from 'next/navigation';
import NewsletterForm from '@/components/newsletter-form';
import Link from 'next/link';
import RelatedNewsWidget from '@/components/RelatedNewsWidget';
import NewsImageWithFallback from '@/components/NewsImageWithFallback';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.alahlyegypt.com';

export async function generateMetadata({ params }: { params: Promise<{ locale: string; slug: string }> }) {
    const { locale, slug } = await params;
    const t = (key: string) => translations[locale]?.[key] || key;
    const res = await fetchApi(`/api/news/category/${slug}`, {
        headers: { 'X-localization': locale },
    });
    if (!res.category) return {};
    const metaTitle = res.category.meta_title || res.category.title;
    const metaDescription = res.category.meta_description || '';
    const metaKeywords = res.category.meta_keyword || undefined;
    const canonicalUrl = `${siteUrl}/${locale}/news/category/${slug}`;
    return {
        title: metaTitle,
        description: metaDescription,
        keywords: metaKeywords,
        alternates: {
            canonical: canonicalUrl,
        },
        openGraph: {
            title: metaTitle,
            description: metaDescription,
            images: res.category.image ? [res.category.image] : [],
            url: canonicalUrl,
        },
        twitter: {
            card: 'summary_large_image',
            title: metaTitle,
            description: metaDescription,
            images: res.category.image ? [res.category.image] : [],
        },
    };
}

export default async function CategoryPage({ params, searchParams }: { params: Promise<{ locale: string; slug: string }>, searchParams: Promise<{ page?: string }> }) {
    const { locale, slug } = await params;
    const { page: pageParam } = await searchParams;
    const t = (key: string) => translations[locale]?.[key] || key;
    const page = Number(pageParam) || 1;
    const limit = 3;
    let data;
    try {
        data = await fetchApi(`/api/news/category/${slug}?limit=${limit}&page=${page}`, {
            headers: { 'X-localization': locale },
        });
    } catch {
        return notFound();
    }
    if (!data?.category) return notFound();
    const { category, categories, news, related } = data;

    return (
        <main className="flex flex-col gap-3 min-h-fit bg-[#F7F7F7] pt-24 lg:pt-[175px] pb-16">
            {/* Breadcrumb */}
            <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
                <ol className="flex flex-wrap items-center gap-1">
                    <li>
                        <a href={`/${locale}/news`} className="hover:underline text-[#C60428] font-bold">{t('news_center') || 'مركز الأخبار'}</a>
                    </li>
                    <li className="mx-1">|</li>
                    <li>
                        {category.title}
                    </li>
                </ol>
            </nav>
            {/* End Breadcrumb */}
            <div className="flex flex-col gap-3 row-start-2 items-center sm:items-start min-h-fit w-full px-2">
                <div className="flex flex-col lg:flex-row gap-8 w-full container mx-auto px-0 sm:px-2 md:px-4">

                    {/* Main content */}
                    <div className="flex-1 min-w-0">
                        {/* Category Title */}
                        <h1 className="text-2xl sm:text-3xl font-bold mb-6 text-black">{category.title}</h1>
                        {/* News List */}
                        <section>
                            {news && news.length > 0 ? news.map((item: any, idx: number) => (
                                <article key={item.id} className="bg-white rounded shadow p-4 mb-8 mt-8">
                                    <div className="flex flex-col md:flex-row gap-6">
                                        <Link href={`/${locale}/news/article/${item.slug}`} className="w-full md:w-1/3 aspect-video relative rounded overflow-hidden block group">
                                            <NewsImageWithFallback
                                                src={item.image_full || '/img/default_placeholder.png'}
                                                alt={item.title}
                                                fill
                                                className="object-cover group-hover:opacity-90 transition-opacity"
                                            />
                                        </Link>
                                        <div className="flex-2 flex flex-col justify-between">
                                            <div>
                                                <div className="text-xs text-gray-500 mb-1">
                                                    {item.published_at
                                                        ? new Date(item.published_at).toLocaleString(
                                                            locale.replace('_', '-'),
                                                            { day: 'numeric', month: 'long', year: 'numeric', hour: '2-digit', minute: '2-digit' }
                                                        )
                                                        : ''}
                                                </div>
                                                <div className="text-xs text-gray-400 mb-1">{category.title}</div>
                                                <h2 className="text-xl font-bold mb-2 text-black">
                                                    <Link href={`/${locale}/news/article/${item.slug}`}>{item.title}</Link>
                                                </h2>
                                                <div className="text-base text-gray-700 mb-2 line-clamp-3">{item.intro}</div>
                                            </div>
                                            <div className="flex justify-end">
                                                <Link href={`/${locale}/news/article/${item.slug}`} className="bg-[#EC2028] text-white px-4 py-2 rounded font-bold text-sm">{t('read_more')}</Link>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            )) : (
                                <div className="text-gray-400 text-center py-12">{t('no_news')}</div>
                            )}
                        </section>
                        {/* Pagination Buttons */}
                        {data.pagination && data.pagination.last_page > 1 && (
                            <div className="flex justify-center mt-8 gap-2">
                                <Link
                                    href={`/${locale}/news/category/${slug}?page=${Math.max(1, data.pagination.current_page - 1)}`}
                                    className={`min-w-[36px] h-10 px-3 rounded font-bold border transition-colors duration-200 flex items-center justify-center
                                        ${data.pagination.current_page <= 1 ? 'bg-gray-100 text-gray-400 pointer-events-none' : 'bg-white border-[#EC2028] hover:bg-[#EC2028] hover:text-white'}`}
                                    aria-disabled={data.pagination.current_page <= 1}
                                >
                                    &lt;
                                </Link>

                                {Array.from({ length: data.pagination.last_page }, (_, i) => {
                                    const pageNum = i + 1;
                                    const currentPage = data.pagination.current_page;

                                    // Only show: first, last, current, and 1 page before/after current
                                    if (
                                        pageNum === 1 ||
                                        pageNum === data.pagination.last_page ||
                                        pageNum === currentPage ||
                                        pageNum === currentPage - 1 ||
                                        pageNum === currentPage + 1
                                    ) {
                                        return (
                                            <Link
                                                key={pageNum}
                                                href={`/${locale}/news/category/${slug}?page=${pageNum}`}
                                                className={`min-w-[36px] h-10 px-3 rounded font-bold border transition-colors duration-200 flex items-center justify-center
                                                    ${currentPage === pageNum ? 'bg-[#EC2028] text-white border-[#EC2028] pointer-events-none' : 'bg-white border-[#EC2028] hover:bg-[#EC2028] hover:text-white'}`}
                                                aria-current={currentPage === pageNum ? 'page' : undefined}
                                            >
                                                {pageNum}
                                            </Link>
                                        );
                                    }

                                    // Show ellipsis (but only once between gaps)
                                    if ((pageNum === 2 && currentPage > 3) || (pageNum === data.pagination.last_page - 1 && currentPage < data.pagination.last_page - 2)) {
                                        return <span key={pageNum} className="px-2 py-1 flex items-center">...</span>;
                                    }

                                    return null;
                                })}

                                <Link
                                    href={`/${locale}/news/category/${slug}?page=${Math.min(data.pagination.last_page, data.pagination.current_page + 1)}`}
                                    className={`min-w-[36px] h-10 px-3 rounded font-bold border transition-colors duration-200 flex items-center justify-center
                                        ${data.pagination.current_page >= data.pagination.last_page ? 'bg-gray-100 text-gray-400 pointer-events-none' : 'bg-white border-[#EC2028] hover:bg-[#EC2028] hover:text-white'}`}
                                    aria-disabled={data.pagination.current_page >= data.pagination.last_page}
                                >
                                    &gt;
                                </Link>
                            </div>
                        )}
                    </div>
                    {/* Aside section */}
                    <aside className="hidden lg:block w-72 flex-shrink-0">
                        {/* Categories */}
                        <div className="rounded p-4 mb-6">
                            <h3 className="font-bold text-lg mb-3 text-black">{t('categories')}</h3>
                            <ul className="space-y-2 w-full">
                                {categories.map((cat: any) => (
                                    <li key={cat.id}>
                                        <Link
                                            href={`/${locale}/news/category/${cat.slug}`}
                                            className={`block px-2 py-1 rounded font-semibold ${cat.id === category.id ? 'bg-[#EC2028] text-white' : 'hover:bg-gray-100 text-black'}`}
                                        >
                                            {cat.title}
                                        </Link>
                                    </li>
                                ))}
                            </ul>
                        </div>
                        {/* Related News */}
                        <RelatedNewsWidget related={related} t={t} locale={locale} />
                        {/* Newsletter Subscription */}
                        <div className="bg-white rounded shadow p-4 mb-6">
                            <h3 className="font-bold text-lg mb-3 text-black">{t('subscribe_newsletter')}</h3>
                            <NewsletterForm tDict={translations[locale]} locale={locale} />
                        </div>
                    </aside>
                </div>
            </div>
        </main>
    );
}

export const dynamic = 'force-dynamic';
