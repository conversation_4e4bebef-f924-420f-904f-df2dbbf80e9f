"use client";

import React from "react";
import { useEffect, useState } from "react";
import translations from "@/lib/locales";
import Image from "next/image";
import { fetchApi } from "@/lib/api";
import Link from "next/link";

export default function AcademyAboutPage({ params }: { params: any }) {
  // Unwrap params if it's a Promise (Next.js App Router migration)
  const { locale } = typeof params.then === 'function' ? React.use(params) : params;
  const t = (key: string) => translations[locale]?.[key] || key;
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [imgLoading, setImgLoading] = useState<Record<number, boolean>>({});
  const [imgSrc, setImgSrc] = useState<Record<number, string>>({});

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetchApi("/api/widgets/academy", { headers: { "X-localization": locale } });
        setData(res.data);
      } catch (err) {
        setError(t("no_data"));
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [locale]);

  if (loading) return (
    <div className="flex flex-col items-center justify-center min-h-[40vh] pt-24 lg:pt-[175px]">
      <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-red-600 mb-4"></div>
      <div className="text-lg text-white">Loading...</div>
    </div>
  );
  if (error || !data) return <div className="text-center text-red-600 py-10 text-lg font-bold">{error || t("no_data")}</div>;

  const { about, activities, news } = data;

  return (
    <main className="flex flex-col bg-[#f6f6f6] min-h-fit pt-24 pb-12 lg:pt-[175px]">
      {/* Breadcrumb */}
      <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
        <ol className="flex flex-wrap items-center gap-1">
          <li>
            {t('alahly_academy')}
          </li>
          <li className="mx-1">|</li>
          <li>{t("about_academy")}</li>
        </ol>
      </nav>
      {/* Header with academy image and overlay title */}
      <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
        <Image
          src={about?.image || "/img/default_bg.png"}
          alt={about?.title || t("about_academy")}
          fill
          className="object-cover w-full h-full"
          priority
        />
        <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
            {(about?.title || t("about_academy")).split('\n').map((line: string, idx: number, arr: string[]) => (
              <React.Fragment key={idx}>
                {line}
                {idx !== arr.length - 1 && <br />}
              </React.Fragment>
            ))}
          </h2>
        </div>
      </div>
      {/* About Section */}
      <section className="max-w-4xl mx-auto w-full py-8 px-4">
        <div className="bg-white rounded-2xl shadow-md p-6 text-lg text-[#222] leading-relaxed">
          {about?.content}
        </div>
        <div className="flex flex-wrap gap-6 mt-6 justify-center">
          <div className="flex flex-col items-center">
            <span className="text-3xl font-bold text-[#C60428]">{about?.players_count}</span>
            <span className="text-[#222] text-sm">{t("players_count")}</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-3xl font-bold text-[#C60428]">{about?.coaches_count}</span>
            <span className="text-[#222] text-sm">{t("coaches_count")}</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-3xl font-bold text-[#C60428]">{about?.championships_count}</span>
            <span className="text-[#222] text-sm">{t("championships_count")}</span>
          </div>
        </div>
      </section>
      {/* Activities Section */}
      <section className="max-w-6xl mx-auto w-full py-12 px-4">
        <h3 className="text-2xl font-bold text-[#C60428] mb-6">{t("activities")}</h3>
        <div className="flex flex-col gap-8">
          {activities?.map((activity: any) => (
            <div key={activity.id} className="bg-white rounded-2xl shadow-md overflow-hidden flex flex-col md:flex-row gap-8 p-6">
              <div className="flex-shrink-0 w-full md:w-1/3 flex items-center justify-center">
                {activity.image ? (
                  <div className="relative w-full h-48 md:h-56 bg-[#f3f3f3] border-2 border-[#eee] overflow-hidden rounded-xl flex flex-col items-center justify-center">
                    <Image
                      src={activity.image}
                      alt={activity.title}
                      fill
                      className="object-cover w-full h-full"
                      style={{ objectPosition: 'top' }}
                    />
                  </div>
                ) : (
                  <div className="w-full h-48 md:h-56 bg-[#f3f3f3] border-2 border-[#eee] flex items-center justify-center rounded-xl">
                    <svg width="48" height="48" fill="#bbb" viewBox="0 0 24 24"><path d="M21 19V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2ZM5 5h14v7.586l-2.293-2.293a1 1 0 0 0-1.414 0L7 19H5V5Zm14 14H7.414l7.293-7.293a1 1 0 0 1 1.414 0L19 15.586V19Z" /></svg>
                  </div>
                )}
              </div>
              <div className="flex-1 flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <span className="h-2 w-2 rounded-full bg-[#C60428] inline-block"></span>
                  <h3 className="text-xl font-bold text-[#C60428]">{activity.title}</h3>
                </div>
                <div className="text-[#222] text-sm leading-relaxed" dangerouslySetInnerHTML={{ __html: activity.content }} />
              </div>
            </div>
          ))}
        </div>
      </section>
      {/* News Section */}
      {news && news.length > 0 && (
        <section className="max-w-6xl mx-auto w-full py-12 px-4">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-[#C60428]">{t("academy_news")}</h3>
            <Link href={`/${locale}/academy/news`} className="px-4 py-2 bg-[#EC2028] rounded text-white font-bold text-base hover:bg-[#C60428] transition-colors">
              {t("more")}
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 sm:gap-3 md:gap-4 p-2 sm:p-3">
            {news && news.slice(0, 2).map((item: any) => (
              <Link key={item.id} href={`/${locale}/academy/news/${item.slug}`} className="bg-white rounded-2xl shadow-md overflow-hidden flex flex-col">
                <div className="relative w-full h-48">
                  <Image
                    src={imgSrc[item.id] ?? item.image_full ?? '/img/default_placeholder.png'}
                    alt={item.title}
                    fill
                    className={`object-cover w-full h-full ${(imgLoading[item.id] ?? true) ? 'opacity-50' : 'opacity-100'}`}
                    onError={() => setImgSrc(s => ({ ...s, [item.id]: '/img/default_placeholder.png' }))}
                    onLoadingComplete={() => setImgLoading(s => ({ ...s, [item.id]: false }))}
                  />
                  {(imgLoading[item.id] ?? true) && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white/60 z-10">
                      <svg className="animate-spin h-8 w-8 text-[#C60428]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                      </svg>
                    </div>
                  )}
                </div>
                <div className="flex-1 p-4 flex flex-col justify-between">
                  <h4 className="font-bold text-lg text-[#C60428] mb-2">{item.title}</h4>
                  <div className="text-[#222] text-sm mb-2 line-clamp-3">{item.intro}</div>
                  <div className="text-end">
                    <span className="text-[#D10128] hover:text-red-800 text-sm font-bold mt-auto">{t("details")}</span>
                  </div>
                </div>
              </Link>
            ))}
            <div className="col-span-1 md:col-span-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3 md:gap-4">
              {news && news.slice(2, 5).map((item: any) => (
                <Link key={item.id} href={`/${locale}/academy/news/${item.slug}`} className="bg-white rounded-2xl shadow-md overflow-hidden flex flex-col">
                  <div className="relative w-full h-48">
                    <Image
                      src={imgSrc[item.id] ?? item.image_full ?? '/img/default_placeholder.png'}
                      alt={item.title}
                      fill
                      className={`object-cover w-full h-full ${(imgLoading[item.id] ?? true) ? 'opacity-50' : 'opacity-100'}`}
                      onError={() => setImgSrc(s => ({ ...s, [item.id]: '/img/default_placeholder.png' }))}
                      onLoadingComplete={() => setImgLoading(s => ({ ...s, [item.id]: false }))}
                    />
                    {(imgLoading[item.id] ?? true) && (
                      <div className="absolute inset-0 flex items-center justify-center bg-white/60 z-10">
                        <svg className="animate-spin h-8 w-8 text-[#C60428]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                        </svg>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 p-4 flex flex-col justify-between">
                    <h4 className="font-bold text-lg text-[#C60428] mb-2">{item.title}</h4>
                    <div className="text-[#222] text-sm mb-2 line-clamp-3">{item.intro}</div>
                    <div className="text-end">
                      <span className="text-[#D10128] hover:text-red-800 text-sm font-bold mt-auto">{t("details")}</span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </section>
      )}
    </main>
  );
}
