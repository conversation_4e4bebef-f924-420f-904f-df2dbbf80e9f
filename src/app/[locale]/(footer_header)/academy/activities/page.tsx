"use client";

import React from "react";
import { useEffect, useState } from "react";
import translations from "@/lib/locales";
import Image from "next/image";
import { fetchApi } from "@/lib/api";

export default function ActivitiesPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = React.use(params);
  const t = (key: string) => translations[locale]?.[key] || key;
  const [activities, setActivities] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchActivities() {
      setLoading(true);
      setError(null);
      try {
        const data = await fetchApi("/api/academy/activities", { headers: { "X-localization": locale } });
        if (data.activities && Array.isArray(data.activities)) {
          setActivities(data.activities);
        } else {
          setError(t("no_activities"));
        }
      } catch (err) {
        setError(t("no_activities"));
      } finally {
        setLoading(false);
      }
    }
    fetchActivities();
  }, [locale]);

  return (
    <main className="flex flex-col bg-[#f6f6f6] min-h-fit pt-24 pb-12 lg:pt-[175px]">
      {/* Breadcrumb */}
      <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
        <ol className="flex flex-wrap items-center gap-1">
          <li>
            {t('alahly_academy')}
          </li>
          <li className="mx-1">|</li>
          <li>
            {t("activities")}
          </li>
        </ol>
      </nav>
      {/* End Breadcrumb */}
      {/* Header with stadium image and overlay title */}
      <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
        <Image
          src="/img/default_bg.png"
          alt={t("activities")}
          fill
          className="object-cover w-full h-full"
          priority
        />
        <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
            {(t("activities")).split('\n').map((line: string, idx: number, arr: string[]) => (
              <React.Fragment key={idx}>
                {line}
                {idx !== arr.length - 1 && <br />}
              </React.Fragment>
            ))}
          </h2>
        </div>
      </div>
      {/* activities grid */}
      <section className="max-w-6xl mx-auto w-full py-12 px-4">
        {loading ? (
          <div className="text-center text-[#C60428] py-10 text-lg font-bold">{t("loading")}</div>
        ) : error ? (
          <div className="text-center text-red-600 py-10 text-lg font-bold">{error}</div>
        ) : (
          <div className="flex flex-col gap-16">
            {activities.map((activity, idx) => (
              <div key={activity.id} className={`bg-white rounded-2xl shadow-md overflow-hidden flex flex-col md:flex-row gap-8 p-6 ${idx % 2 === 1 ? 'md:flex-row-reverse' : ''}`}>
                {/* Branch Image */}
                <div className="flex-shrink-0 w-full md:w-1/3 flex items-center justify-center">
                  {activity.image ? (
                    <div className="relative w-full h-48 md:h-56 bg-[#f3f3f3] border-2 border-[#eee] overflow-hidden rounded-xl flex flex-col items-center justify-center">
                      <Image
                        src={activity.image}
                        alt={activity.alt_text || activity.title}
                        fill
                        className="object-cover w-full h-full"
                        style={{ objectPosition: 'top' }}
                      />
                      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-white/80 px-3 py-1 rounded text-center text-[#C60428] font-bold text-base w-fit max-w-full truncate">
                        {activity.title}
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-48 md:h-56 bg-[#f3f3f3] border-2 border-[#eee] flex items-center justify-center rounded-xl">
                      <svg width="48" height="48" fill="#bbb" viewBox="0 0 24 24"><path d="M21 19V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2ZM5 5h14v7.586l-2.293-2.293a1 1 0 0 0-1.414 0L7 19H5V5Zm14 14H7.414l7.293-7.293a1 1 0 0 1 1.414 0L19 15.586V19Z" /></svg>
                    </div>
                  )}
                </div>
                {/* Branch Content */}
                <div className="flex-1 flex flex-col gap-2">
                  <div className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-[#C60428] inline-block"></span>
                    <h3 className="text-xl font-bold text-[#C60428]">{activity.title}</h3>
                  </div>
                  <div className="text-[#222] text-sm leading-relaxed">{activity.content}</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </section>
    </main>
  );
}
