import { getCachedNewsList } from '@/lib/cache-helpers';
import translations from '@/lib/locales';
import Image from 'next/image';
import NewsGridPage from '@/components/academy/news/news-grid';
import React from 'react';

// ISR configuration - revalidate every 5 minutes for news listings
export const revalidate = 300;

export default async function SocialResponsibilityPage({ params }: { params: Promise<{ locale: string }> }) {
    const { locale } = await params;
    const t = (key: string) => translations[locale]?.[key] || key;
    const data = await getCachedNewsList('/api/academy/news', locale, ['academy-news']);

    if (!data) {
        return (
            <main className="flex flex-col min-h-fit pt-24 lg:pt-[175px] pb-24 items-center justify-center">
                <div className="text-red-600 text-xl font-bold mt-20">{t('error_message') || 'حدث خطأ أثناء تحميل الأخبار.'}</div>
            </main>
        );
    }
    if (!data || !data.news) {
        return (
            <main className="flex flex-col min-h-fit pt-24 lg:pt-[175px] pb-24 items-center justify-center">
                <div className="text-red-600 text-xl font-bold mt-20">{t('error_message') || 'حدث خطأ أثناء تحميل الأخبار.'}</div>
            </main>
        );
    }
    const { news, pagination } = data;

    return (
        <main className="flex flex-col bg-[#f6f6f6] min-h-fit pt-24 pb-24 lg:pt-[175px]">
            {/* Breadcrumb */}
            <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
                <ol className="flex flex-wrap items-center gap-1">
                    <li>
                        {t('alahly_academy') || 'النادي'}
                    </li>
                    <li className="mx-1">|</li>
                    <li>
                        {t('academy_news')}
                    </li>
                </ol>
            </nav>
            {/* End Breadcrumb */}
            {/* Header with stadium image and overlay title */}
            <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
                <Image
                    src="/img/default_bg.png"
                    alt={t("academy_news")}
                    fill
                    className="object-cover w-full h-full"
                    priority
                />
                <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
                    <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
                        {(t("academy_news")).split('\n').map((line: string, idx: number, arr: string[]) => (
                            <React.Fragment key={idx}>
                                {line}
                                {idx !== arr.length - 1 && <br />}
                            </React.Fragment>
                        ))}
                    </h2>
                </div>
            </div>
            {/* Main Content and Sidebar */}
            <div className="container mt-4 mx-auto px-2 md:px-4 flex flex-col-reverse lg:flex-row gap-8">
                {/* Main Content */}
                <section className="flex-1">
                    <NewsGridPage locale={locale} initialNews={news} initialPagination={pagination} />
                </section>
            </div>
        </main>
    );
}
