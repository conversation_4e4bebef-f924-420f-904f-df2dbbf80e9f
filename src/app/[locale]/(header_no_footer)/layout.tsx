import "../../globals.css";
import Navbar from "@/components/navbar";
import RouteLoading from '@/components/route-loading';
import { dir } from 'i18next';
import { use } from 'react';
import { GlobalDataProvider } from '@/context/GlobalDataContext';
import translations from "@/lib/locales";
import type { Metadata } from "next";
import Footer from "@/components/footer";

const defaultLocale = 'ar';
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.alahlyegypt.com';

export async function generateMetadata({ params }: { params: any }): Promise<Metadata> {
  // Await params if it's a Promise (for dynamic routes)
  const resolvedParams = typeof params?.then === 'function' ? await params : params;
  const locale = resolvedParams?.locale || defaultLocale;
  const t = (key: string) => translations[locale]?.[key] || key;
  return {
    title: t("site_title"),
    description: t("site_description") || "A website dedicated for Al Ahly SC",
    alternates: {
      canonical: `${siteUrl}/${locale}`,
    },
  };
}

export default function RootLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode;
  params: any
}>) {
  // Support both sync and async params (for Next.js 15+)
  const resolvedParams = typeof params?.then === 'function' ? use(params) : params;
  const locale = resolvedParams?.locale || defaultLocale;

  return (
    <html lang={locale} dir={dir(locale)}>
      <body dir={dir(locale)} className={"antialiased"}>
        <GlobalDataProvider>
          <Navbar />
          <RouteLoading />
          {children}
        </GlobalDataProvider>
      </body>
    </html>
  );
}