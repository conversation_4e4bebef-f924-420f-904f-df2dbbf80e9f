import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import translations from "@/lib/locales";
import Image from "next/image";

export default function NotFoundPage() {
  // Default to Arabic locale, or you can use a server-side detection if needed
  const locale = "ar";
  const t = (key: string) => translations[locale]?.[key] || key;

  return (
    <div className="min-h-screen flex flex-col bg-[#f5f5f5]">
      <Navbar />
      <main className="flex flex-1 flex-col items-center justify-center text-center py-24 px-4">
        <Image src="/img/hero-pic2.png" alt="404" width={320} height={220} className="mx-auto mb-8" />
        <h1 className="text-5xl font-bold text-[#C60428] mb-4">404</h1>
        <h2 className="text-2xl md:text-3xl font-bold mb-4 text-[#222]">{t("not_found_title") || "الصفحة غير موجودة"}</h2>
        <p className="text-lg text-gray-600 mb-8">{t("not_found_message") || "عذراً، الصفحة التي تبحث عنها غير متوفرة أو تم نقلها."}</p>
        <a href={`/${locale}`} className="inline-block bg-[#C60428] text-white px-8 py-3 rounded font-bold text-lg hover:bg-[#a0021f] transition">{t("back_to_home") || "العودة للرئيسية"}</a>
      </main>
      <Footer />
    </div>
  );
}
