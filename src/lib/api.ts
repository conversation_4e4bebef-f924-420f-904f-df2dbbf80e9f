import axios from "axios";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";

export async function fetchApi(endpoint: string, options: RequestInit = {}) {
  // If endpoint is a full URL, use it directly, else use API_BASE_URL
  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
  try {
    const axiosOptions: any = {
      method: options.method || 'GET',
      url,
      headers: options.headers,
      data: options.body,
      // Spread any other options if needed
      ...options,
    };
    // Remove data if not needed (GET requests)
    if (!axiosOptions.data) delete axiosOptions.data;
    const res = await axios(axiosOptions);
    return res.data;
  } catch (err: any) {
    if (err.response) {
      throw new Error(`API error: ${err.response.status}`);
    }
    throw err;
  }
}

export async function searchNews(query: string, locale: string, page: number = 1, limit: number = 5) {
  try {
    const searchParams = new URLSearchParams({
      q: query,
      page: page.toString(),
      limit: limit.toString()
    });

    const response = await fetchApi(`/api/news/search?${searchParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-localization': locale,
      },
    });

    return response;
  } catch (error) {
    console.error('Search API error:', error);
    throw error;
  }
}
