import { unstable_cache } from 'next/cache';
import { fetchApi } from './api';

// Reusable cache function for news-related data
export const getCachedNewsData = unstable_cache(
  async (endpoint: string, locale: string, tags: string[] = []) => {
    try {
      const data = await fetchApi(endpoint, {
        headers: { 'X-localization': locale },
        next: { tags },
      });
      return data;
    } catch (error) {
      console.error(`Failed to fetch from ${endpoint}`, error);
      return null;
    }
  },
  ['news-data'],
  { revalidate: 3600, tags: ['news'] }
);

// Generic cached API fetcher with configurable revalidation
export const getCachedApiData = (revalidateTime: number, cacheKey: string, cacheTags: string[] = []) =>
  unstable_cache(
    async (endpoint: string, locale: string, additionalTags: string[] = []) => {
      try {
        const allTags = [...cacheTags, ...additionalTags];
        const data = await fetchApi(endpoint, {
          headers: { 'X-localization': locale },
          next: { tags: allTags },
        });
        return data;
      } catch (error) {
        console.error(`Failed to fetch from ${endpoint}`, error);
        return null;
      }
    },
    [cacheKey],
    { revalidate: revalidateTime, tags: cacheTags }
  );

// Specific cache functions for different content types
export const getCachedNewsArticle = getCachedApiData(3600, 'news-article', ['news']);
export const getCachedNewsList = getCachedApiData(1800, 'news-list', ['news']);
export const getCachedHomeData = getCachedApiData(900, 'home-data', ['home']);
export const getCachedStaticData = getCachedApiData(86400, 'static-data', ['static']);
export const getCachedClubData = getCachedApiData(86400, 'club-data', ['club']);
export const getCachedFootballData = getCachedApiData(3600, 'football-data', ['football']);
export const getCachedAcademyData = getCachedApiData(3600, 'academy-data', ['academy']);