import { unstable_cache } from 'next/cache';
import { fetchApi } from './api';

// Reusable cache function for news-related data
export const getCachedNewsData = (endpoint: string, locale: string, tags: string[] = []) => {
  const uniqueCacheKey = `news-data-${locale}-${endpoint}`;
  const allTags = ['news', `locale-${locale}`, ...tags];

  return unstable_cache(
    async () => {
      try {
        const data = await fetchApi(endpoint, {
          headers: { 'X-localization': locale },
          next: { tags: allTags },
        });
        return data;
      } catch (error) {
        console.error(`Failed to fetch from ${endpoint}`, error);
        return null;
      }
    },
    [uniqueCacheKey],
    { revalidate: 3600, tags: allTags }
  )();
};

// Generic cached API fetcher with configurable revalidation
export const getCachedApiData = (revalidateTime: number, cacheKey: string, cacheTags: string[] = []) => {
  return (endpoint: string, locale: string, additionalTags: string[] = []) => {
    // Create a unique cache key that includes locale
    const uniqueCacheKey = `${cacheKey}-${locale}-${endpoint}`;
    const allTags = [...cacheTags, ...additionalTags, `locale-${locale}`];

    return unstable_cache(
      async () => {
        try {
          const data = await fetchApi(endpoint, {
            headers: { 'X-localization': locale },
            next: { tags: allTags },
          });
          return data;
        } catch (error) {
          console.error(`Failed to fetch from ${endpoint}`, error);
          return null;
        }
      },
      [uniqueCacheKey],
      { revalidate: revalidateTime, tags: allTags }
    )();
  };
};

// Specific cache functions for different content types
export const getCachedNewsArticle = getCachedApiData(3600, 'news-article', ['news']);
export const getCachedNewsList = getCachedApiData(1800, 'news-list', ['news']);
export const getCachedHomeData = getCachedApiData(900, 'home-data', ['home']);
export const getCachedStaticData = getCachedApiData(86400, 'static-data', ['static']);
export const getCachedClubData = getCachedApiData(86400, 'club-data', ['club']);
export const getCachedFootballData = getCachedApiData(3600, 'football-data', ['football']);
export const getCachedAcademyData = getCachedApiData(3600, 'academy-data', ['academy']);