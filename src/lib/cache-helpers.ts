import { unstable_cache } from 'next/cache';
import { fetchApi } from './api';

// Reusable cache function for news-related data
export const getCachedNewsData = unstable_cache(
  async (endpoint: string, locale: string, tags: string[] = []) => {
    try {
      const data = await fetchApi(endpoint, {
        headers: { 'X-localization': locale },
        next: { tags },
      });
      return data;
    } catch (error) {
      console.error(`Failed to fetch from ${endpoint}`, error);
      return null;
    }
  },
  ['news-data'],
  { revalidate: 3600, tags: ['news'] }
);