import { unstable_cache } from 'next/cache';
import { fetchApi } from './api';

// Reusable cache function for news-related data
export const getCachedNewsData = (endpoint: string, locale: string, tags: string[] = []) => {
  const uniqueCacheKey = `news-data-${locale}-${endpoint}`;
  const allTags = ['news', `locale-${locale}`, ...tags];

  return unstable_cache(
    async () => {
      try {
        const data = await fetchApi(endpoint, {
          headers: { 'X-localization': locale },
          next: { tags: allTags },
        });
        return data;
      } catch (error) {
        console.error(`Failed to fetch from ${endpoint}`, error);
        return null;
      }
    },
    [uniqueCacheKey],
    { revalidate: 3600, tags: allTags }
  )();
};

// Generic cached API fetcher with configurable revalidation
export const getCachedApiData = (revalidateTime: number, cacheKey: string, cacheTags: string[] = []) => {
  return (endpoint: string, locale: string, additionalTags: string[] = []) => {
    // Create a unique cache key that includes locale
    const uniqueCacheKey = `${cacheKey}-${locale}-${endpoint}`;
    const allTags = [...cacheTags, ...additionalTags, `locale-${locale}`];

    return unstable_cache(
      async () => {
        try {
          const data = await fetchApi(endpoint, {
            headers: { 'X-localization': locale },
            next: { tags: allTags },
          });
          return data;
        } catch (error) {
          console.error(`Failed to fetch from ${endpoint}`, error);
          return null;
        }
      },
      [uniqueCacheKey],
      { revalidate: revalidateTime, tags: allTags }
    )();
  };
};

// Specific cache functions for different content types
export const getCachedNewsArticle = getCachedApiData(900, 'news-article', ['news']); // 15 minutes
export const getCachedNewsList = getCachedApiData(300, 'news-list', ['news']); // 5 minutes
export const getCachedHomeData = getCachedApiData(900, 'home-data', ['home']); // 15 minutes
export const getCachedStaticData = getCachedApiData(86400, 'static-data', ['static']); // 24 hours
export const getCachedClubData = getCachedApiData(86400, 'club-data', ['club']); // 24 hours
export const getCachedFootballData = getCachedApiData(900, 'football-data', ['football']); // 15 minutes
export const getCachedAcademyData = getCachedApiData(900, 'academy-data', ['academy']); // 15 minutes