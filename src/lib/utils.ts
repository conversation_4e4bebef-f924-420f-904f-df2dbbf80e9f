import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Post-processes HTML to ensure Facebook/YouTube iframes have the correct sandbox and allow attributes for CSP compatibility.
 */
export function fixEmbedIframes(html: string): string {
  if (!html) return html;
  // Facebook: DO NOT set sandbox, only allow
  html = html.replace(
    /<iframe([^>]+src=["'][^"']*facebook\.com[^"']*["'][^>]*)>/gi,
    (match, attrs) => {
      let newAttrs = attrs
        .replace(/\s?sandbox=["'][^"']*["']/gi, "")
        .replace(/\s?allow=["'][^"']*["']/gi, "");
      return `<iframe${newAttrs} allow="encrypted-media; picture-in-picture; autoplay; clipboard-write; fullscreen">`;
    }
  );
  // YouTube: sandbox is OK
  html = html.replace(
    /<iframe([^>]+src=["'][^"']*youtube\.com[^"']*["'][^>]*)>/gi,
    (match, attrs) => {
      let newAttrs = attrs
        .replace(/\s?sandbox=["'][^"']*["']/gi, "")
        .replace(/\s?allow=["'][^"']*["']/gi, "");
      return `<iframe${newAttrs} allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen" sandbox="allow-scripts allow-same-origin allow-popups allow-forms">`;
    }
  );
  return html;
}
