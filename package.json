{"name": "al-<PERSON><PERSON>-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-slot": "^1.2.2", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "flickity": "^3.0.0", "i18next": "^25.2.0", "js-base64": "^3.7.7", "lucide-react": "^0.511.0", "next": "15.3.2", "next-i18next": "^15.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.1", "react-pageflip": "^2.0.3", "react-pdf": "^9.2.1", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "swiper": "^11.2.6", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/flickity": "^2.2.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-pdf": "^6.2.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}